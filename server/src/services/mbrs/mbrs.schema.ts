// TypeBox schema for mbrs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, PhoneSchema } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const mbrsSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: ObjectIdSchema(),

  // Add only_in_json properties:
  coverage: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  pm: Type.Optional(Type.String()),
  inactive: Type.Optional(Type.Boolean()),
  itemId: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // firstName: Type.String(), // only_in_typebox
  // lastName: Type.String(), // only_in_typebox
  // middleName: Type.Optional(Type.String()), // only_in_typebox
  // suffix: Type.Optional(Type.String()), // only_in_typebox
  // prefix: Type.Optional(Type.String()), // only_in_typebox
  // nickname: Type.Optional(Type.String()), // only_in_typebox
  // dateOfBirth: Type.Optional(Type.Any()), // only_in_typebox
  // gender: Type.Optional(Type.String()), // only_in_typebox
  // ssn: Type.Optional(Type.String()), // only_in_typebox
  // email: Type.Optional(Type.String()), // only_in_typebox
  // phone: Type.Optional(PhoneSchema), // only_in_typebox
  // address: Type.Optional(ServiceAddressSchema), // only_in_typebox
  // emergencyContact: Type.Optional(Type.Object({...})), // only_in_typebox
  // household: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // relationship: Type.Optional(Type.String()), // only_in_typebox
  // dependent: Type.Optional(Type.Boolean()), // only_in_typebox
  // student: Type.Optional(Type.Boolean()), // only_in_typebox
  // disabled: Type.Optional(Type.Boolean()), // only_in_typebox
  // tobacco: Type.Optional(Type.Boolean()), // only_in_typebox
  // pregnant: Type.Optional(Type.Boolean()), // only_in_typebox
  // enrollments: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // claims: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // visits: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // medications: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // conditions: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // allergies: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // notes: Type.Optional(Type.String()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Mbrs = Static<typeof mbrsSchema>
export const mbrsValidator = getValidator(mbrsSchema, dataValidator)
export const mbrsResolver = resolve<Mbrs, HookContext>({})
export const mbrsExternalResolver = resolve<Mbrs, HookContext>({})

// Schema for creating new data
export const mbrsDataSchema = Type.Object({
  ...Type.Omit(mbrsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MbrsData = Static<typeof mbrsDataSchema>
export const mbrsDataValidator = getValidator(mbrsDataSchema, dataValidator)
export const mbrsDataResolver = resolve<MbrsData, HookContext>({})

// Schema for updating existing data
export const mbrsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(mbrsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch:
  coverage: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  pm: Type.Optional(Type.String()),
  inactive: Type.Optional(Type.Boolean()),
  itemId: Type.Optional(Type.String())
}, { additionalProperties: false })

export type MbrsPatch = Static<typeof mbrsPatchSchema>
export const mbrsPatchValidator = getValidator(mbrsPatchSchema, dataValidator)
export const mbrsPatchResolver = resolve<MbrsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const mbrsQueryProperties = mbrsSchema

export const mbrsQuerySchema = querySyntax(mbrsQueryProperties)

export type MbrsQuery = Static<typeof mbrsQuerySchema>
export const mbrsQueryValidator = getValidator(mbrsQuerySchema, queryValidator)
export const mbrsQueryResolver = resolve<MbrsQuery, HookContext>({})
