// TypeBox schema for refs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const refsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),

  // Add only_in_json properties:
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  person: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  disabled: Type.Optional(Type.Boolean()),
  disabledBy: Type.Optional(ObjectIdSchema()),
  disabledAt: Type.Optional(Type.Any()),
  isHost: Type.Optional(Type.Boolean()),
  showBy: Type.Optional(ObjectIdSchema()),
  npn: Type.Optional(Type.String()),
  sendTo: Type.Optional(Type.String()),
  approved: Type.Optional(Type.Boolean()),
  approvedAt: Type.Optional(Type.Any()),
  approvedBy: Type.Optional(ObjectIdSchema()),
  leads: Type.Optional(Type.Array(ObjectIdSchema())),
  tags: Type.Optional(Type.Array(Type.String())),
  phone: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  calendar: Type.Optional(ObjectIdSchema()),
  calendars: Type.Optional(Type.Array(ObjectIdSchema())),
  contract: Type.Optional(ObjectIdSchema()),
  status: Type.Optional(Type.String()),
  teams: Type.Optional(Type.Array(ObjectIdSchema())),

  // Remove only_in_typebox properties (commenting out):
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // reference: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // referenceType: Type.Optional(Type.String()), // only_in_typebox
  // value: Type.Optional(Type.Any()), // only_in_typebox
  // metadata: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Refs = Static<typeof refsSchema>
export const refsValidator = getValidator(refsSchema, dataValidator)
export const refsResolver = resolve<Refs, HookContext>({})
export const refsExternalResolver = resolve<Refs, HookContext>({})

export const refsDataSchema = Type.Object({
  ...Type.Omit(refsSchema, ['_id']).properties
}, { additionalProperties: false })

export type RefsData = Static<typeof refsDataSchema>
export const refsDataValidator = getValidator(refsDataSchema, dataValidator)
export const refsDataResolver = resolve<RefsData, HookContext>({})

export const refsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(refsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  person: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  disabled: Type.Optional(Type.Boolean()),
  disabledBy: Type.Optional(ObjectIdSchema()),
  disabledAt: Type.Optional(Type.Any()),
  isHost: Type.Optional(Type.Boolean()),
  showBy: Type.Optional(ObjectIdSchema()),
  npn: Type.Optional(Type.String()),
  sendTo: Type.Optional(Type.String()),
  approved: Type.Optional(Type.Boolean()),
  approvedAt: Type.Optional(Type.Any()),
  approvedBy: Type.Optional(ObjectIdSchema()),
  leads: Type.Optional(Type.Array(ObjectIdSchema())),
  tags: Type.Optional(Type.Array(Type.String())),
  phone: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  calendar: Type.Optional(ObjectIdSchema()),
  calendars: Type.Optional(Type.Array(ObjectIdSchema())),
  contract: Type.Optional(ObjectIdSchema()),
  status: Type.Optional(Type.String()),
  teams: Type.Optional(Type.Array(ObjectIdSchema()))
}, { additionalProperties: false })
export type RefsPatch = Static<typeof refsPatchSchema>
export const refsPatchValidator = getValidator(refsPatchSchema, dataValidator)
export const refsPatchResolver = resolve<RefsPatch, HookContext>({})

// Allow querying on any field from the main schema
const refsQueryProperties = Type.Object({
  ...refsSchema.properties,
  // Add only_in_json query properties:
  _limit_to: Type.Optional(Type.Any()),
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  person: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  disabled: Type.Optional(Type.Boolean()),
  disabledBy: Type.Optional(ObjectIdSchema()),
  disabledAt: Type.Optional(Type.Any()),
  isHost: Type.Optional(Type.Boolean()),
  showBy: Type.Optional(ObjectIdSchema()),
  npn: Type.Optional(Type.String()),
  sendTo: Type.Optional(Type.String()),
  approved: Type.Optional(Type.Boolean()),
  approvedAt: Type.Optional(Type.Any()),
  approvedBy: Type.Optional(ObjectIdSchema()),
  leads: Type.Optional(Type.Array(ObjectIdSchema())),
  tags: Type.Optional(Type.Array(Type.String())),
  phone: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  calendar: Type.Optional(ObjectIdSchema()),
  calendars: Type.Optional(Type.Array(ObjectIdSchema())),
  contract: Type.Optional(ObjectIdSchema()),
  status: Type.Optional(Type.String()),
  teams: Type.Optional(Type.Array(ObjectIdSchema())),
  // Fix type mismatch:
  name: Type.Optional(Type.Any()) // type_mismatch_json_unknown_typebox_string
}, { additionalProperties: false })

export const refsQuerySchema = querySyntax(refsQueryProperties)
export type RefsQuery = Static<typeof refsQuerySchema>
export const refsQueryValidator = getValidator(refsQuerySchema, queryValidator)
export const refsQueryResolver = resolve<RefsQuery, HookContext>({})
