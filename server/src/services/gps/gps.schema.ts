// TypeBox schema for gps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

export const gpsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),

  // Keep existing fields that aren't marked as only_in_typebox:
  org: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),

  // Fix type mismatch: runRequest should be unknown (not string)
  runRequest: Type.Optional(Type.Unknown()),

  // Add only_in_json properties:
  companyName: Type.Optional(Type.String()),
  companyAvatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  email: Type.Optional(Type.String()),
  eeCount: Type.Optional(Type.Number()),
  planName: Type.Optional(Type.String()),
  employerContribution: Type.Optional(Type.Number()),
  ale: Type.Optional(Type.Boolean()),
  owner: Type.Optional(ObjectIdSchema()),
  editors: Type.Optional(Type.Array(ObjectIdSchema())),
  vectorId: Type.Optional(Type.String()),
  lastSim: Type.Optional(Type.Any()),
  groupCompare: Type.Optional(Type.Record(Type.String(), Type.Any())),
  simProgress: Type.Optional(Type.Record(Type.String(), Type.Any())),
  simStats: Type.Optional(Type.Record(Type.String(), Type.Any())),
  currentStats: Type.Optional(Type.Record(Type.String(), Type.Any())),
  employees: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  employerContributionReports: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),

  // Remove only_in_typebox properties (commenting out):
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Gps = Static<typeof gpsSchema>
export const gpsValidator = getValidator(gpsSchema, dataValidator)
export const gpsResolver = resolve<Gps, HookContext>({})
export const gpsExternalResolver = resolve<Gps, HookContext>({})

export const gpsDataSchema = Type.Object({
  ...Type.Omit(gpsSchema, ['_id']).properties
}, { additionalProperties: false })

export type GpsData = Static<typeof gpsDataSchema>
export const gpsDataValidator = getValidator(gpsDataSchema, dataValidator)
export const gpsDataResolver = resolve<GpsData, HookContext>({})

export const gpsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(gpsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type GpsPatch = Static<typeof gpsPatchSchema>
export const gpsPatchValidator = getValidator(gpsPatchSchema, dataValidator)
export const gpsPatchResolver = resolve<GpsPatch, HookContext>({})

// Allow querying on any field from the main schema
const gpsQueryProperties = gpsSchema
export const gpsQuerySchema = querySyntax(gpsQueryProperties)
export type GpsQuery = Static<typeof gpsQuerySchema>
export const gpsQueryValidator = getValidator(gpsQuerySchema, queryValidator)
export const gpsQueryResolver = resolve<GpsQuery, HookContext>({})
