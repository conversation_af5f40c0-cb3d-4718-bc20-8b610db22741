// TypeBox schema for groups service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const groupsSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),

  // Add only_in_json properties:
  applyUcan: Type.Optional(Type.Boolean()),
  healthPlans: Type.Optional(Type.Array(ObjectIdSchema())),
  planClass: Type.Optional(Type.String()),
  memberCount: Type.Optional(Type.Number()),
  memberCountAt: Type.Optional(Type.Any()),

  // Keep existing fields that aren't marked as only_in_typebox:
  key: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // type: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // members: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // managers: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // plans: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // coverages: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // enrollments: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // effectiveDate: Type.Optional(Type.Any()), // only_in_typebox
  // terminationDate: Type.Optional(Type.Any()), // only_in_typebox
  // renewalDate: Type.Optional(Type.Any()), // only_in_typebox
  // minimumParticipation: Type.Optional(Type.Number()), // only_in_typebox
  // currentParticipation: Type.Optional(Type.Number()), // only_in_typebox
  // premiumContribution: Type.Optional(Type.Number()), // only_in_typebox
  // waitingPeriod: Type.Optional(Type.Number()), // only_in_typebox
  // eligibilityRules: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // settings: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Groups = Static<typeof groupsSchema>
export const groupsValidator = getValidator(groupsSchema, dataValidator)
export const groupsResolver = resolve<Groups, HookContext>({})
export const groupsExternalResolver = resolve<Groups, HookContext>({})

// Schema for creating new data
export const groupsDataSchema = Type.Object({
  ...Type.Omit(groupsSchema, ['_id']).properties
}, { additionalProperties: false })

export type GroupsData = Static<typeof groupsDataSchema>
export const groupsDataValidator = getValidator(groupsDataSchema, dataValidator)
export const groupsDataResolver = resolve<GroupsData, HookContext>({})

// Schema for updating existing data
export const groupsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(groupsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })

export type GroupsPatch = Static<typeof groupsPatchSchema>
export const groupsPatchValidator = getValidator(groupsPatchSchema, dataValidator)
export const groupsPatchResolver = resolve<GroupsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const groupsQueryProperties = groupsSchema

export const groupsQuerySchema = querySyntax(groupsQueryProperties)

export type GroupsQuery = Static<typeof groupsQuerySchema>
export const groupsQueryValidator = getValidator(groupsQuerySchema, queryValidator)
export const groupsQueryResolver = resolve<GroupsQuery, HookContext>({})
