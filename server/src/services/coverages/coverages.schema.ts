// TypeBox schema for coverages service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, VideoSchema, GeoJsonFeatureSchema, TaxSchema, typeboxToJsonSchema, addToSet } from '../../utils/common/typebox-schemas.js'

// Base coverage schema for calculations
const BaseCoverageSchema = Type.Object({
  org: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  deductible: Type.Optional(Type.Number()),
  outOfPocketMax: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  premium: Type.Optional(Type.Number()),
  benefits: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

// Coverage calculation schema (for OpenAI API)
const CoverageCalcSchema = Type.Intersect([
  BaseCoverageSchema,
  Type.Object({
    covered: Type.Union([Type.Literal('individual'), Type.Literal('group')])
  })
])

// Export JSON Schema version for OpenAI API compatibility
export const coverageCalcSchema = typeboxToJsonSchema(CoverageCalcSchema)

// Export additional schemas for backward compatibility
export const fixedRate = Type.Object({
  single: Type.Optional(Type.Number()),
  family: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Main coverages schema
export const coveragesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),

  // Add only_in_json properties:
  carrierName: Type.Optional(Type.String()),
  webpage: Type.Optional(Type.String()),
  openNetwork: Type.Optional(Type.Boolean()),
  plan_type: Type.Optional(Type.String()),
  hsaQualified: Type.Optional(Type.Boolean()),
  productDetailRef: Type.Optional(Type.String()),
  fortyPremium: Type.Optional(Type.Number()),
  maxAge: Type.Optional(Type.Number()),
  preventive: Type.Optional(Type.Boolean()),
  coins: Type.Optional(Type.Record(Type.String(), Type.Any())),
  deductibles: Type.Optional(Type.Record(Type.String(), Type.Any())),
  cap: Type.Optional(Type.Record(Type.String(), Type.Any())),
  caps: Type.Optional(Type.Record(Type.String(), Type.Any())),
  copays: Type.Optional(Type.Record(Type.String(), Type.Any())),
  rates: Type.Optional(Type.Record(Type.String(), Type.Any())),
  deductibleType: Type.Optional(Type.String()),
  moop: Type.Optional(Type.Record(Type.String(), Type.Any())),
  moops: Type.Optional(Type.Record(Type.String(), Type.Any())),
  monthsSinceSmoked: Type.Optional(Type.Number()),
  catsBlacklist: Type.Optional(Type.Array(ObjectIdSchema())),

  // Fix type mismatches (object vs number):
  coinsurance: Type.Optional(Type.Record(Type.String(), Type.Any())), // was Type.Number()
  deductible: Type.Optional(Type.Record(Type.String(), Type.Any())), // was Type.Number()
  premium: Type.Optional(Type.Record(Type.String(), Type.Any())), // was Type.Number()

  // Pattern properties: benefits.[^.*$] - use Record for pattern properties
  benefits: Type.Optional(Type.Record(Type.String(), Type.Record(Type.String(), Type.Any()))),

  // Remove only_in_typebox properties (commenting out):
  // org: ObjectIdSchema(), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // outOfPocketMax: Type.Optional(Type.Number()), // only_in_typebox
  // copay: Type.Optional(Type.Number()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox
  // vectorIds: Type.Optional(ObjectIdSchema()), // only_in_typebox (but has type mismatch in patch)
  // uploadIds: Type.Optional(...), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Coverages = Static<typeof coveragesSchema>
export const coveragesValidator = getValidator(coveragesSchema, dataValidator)
export const coveragesResolver = resolve<Coverages, HookContext>({})
export const coveragesExternalResolver = resolve<Coverages, HookContext>({})

// Schema for creating new data
export const coveragesDataSchema = Type.Object({
  ...Type.Omit(coveragesSchema, ['_id']).properties
}, { additionalProperties: false })

export type CoveragesData = Static<typeof coveragesDataSchema>
export const coveragesDataValidator = getValidator(coveragesDataSchema, dataValidator)
export const coveragesDataResolver = resolve<CoveragesData, HookContext>({})

// Schema for updating existing data
export const coveragesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(coveragesSchema, ['_id'])).properties,

  // Fix type mismatch: vectorIds should be object (not string) in patch schema
  vectorIds: Type.Optional(Type.Record(Type.String(), Type.Any())),

  // MongoDB operators using utility functions
  $addToSet: Type.Optional(addToSet([]))
}, { additionalProperties: false })
export type CoveragesPatch = Static<typeof coveragesPatchSchema>
export const coveragesPatchValidator = getValidator(coveragesPatchSchema, dataValidator)
export const coveragesPatchResolver = resolve<CoveragesPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const coveragesQueryProperties = coveragesSchema

export const coveragesQuerySchema = querySyntax(coveragesQueryProperties)
export type CoveragesQuery = Static<typeof coveragesQuerySchema>
export const coveragesQueryValidator = getValidator(coveragesQuerySchema, queryValidator)
export const coveragesQueryResolver = resolve<CoveragesQuery, HookContext>({})

// Export for backward compatibility
export const coverCopySchema = coveragesSchema
