// TypeBox schema for cats service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, pull, addToSet } from '../../utils/common/typebox-schemas.js'

export const catsSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Required fields from JSON Schema
  name: Type.String(),

  // Properties from JSON Schema
  avatar: Type.Optional(ImageSchema), // Note: Object in JSON Schema, not String
  org: Type.Optional(ObjectIdSchema()),
  managers: Type.Optional(Type.Array(ObjectIdSchema())), // Note: Array in JSON Schema, not String
  writers: Type.Optional(Type.Array(ObjectIdSchema())),
  images: Type.Optional(Type.Array(ImageSchema)),
  description: Type.Optional(Type.String()),
  code_regex: Type.Optional(Type.String()),
  conditions: Type.Optional(Type.Array(ObjectIdSchema())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  meds: Type.Optional(Type.Array(ObjectIdSchema())),

  // Remove TypeBox-only properties that aren't in JSON Schema:
  // type: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: true }) // Note: JSON Schema has additionalProperties: true

export type Cats = Static<typeof catsSchema>
export const catsValidator = getValidator(catsSchema, dataValidator)
export const catsResolver = resolve<Cats, HookContext>({})
export const catsExternalResolver = resolve<Cats, HookContext>({})

export const catsDataSchema = Type.Object({
  ...Type.Omit(catsSchema, ['_id']).properties
}, { additionalProperties: true }) // Note: JSON Schema has additionalProperties: true

export type CatsData = Static<typeof catsDataSchema>
export const catsDataValidator = getValidator(catsDataSchema, dataValidator)
export const catsDataResolver = resolve<CatsData, HookContext>({})

export const catsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(catsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type CatsPatch = Static<typeof catsPatchSchema>
export const catsPatchValidator = getValidator(catsPatchSchema, dataValidator)
export const catsPatchResolver = resolve<CatsPatch, HookContext>({})

// Allow querying on any field from the main schema
const catsQueryProperties = Type.Object({
  ...catsSchema.properties,
  // Override name to allow any type for queries (matching JSON Schema)
  name: Type.Optional(Type.Any())
}, { additionalProperties: true })

export const catsQuerySchema = querySyntax(catsQueryProperties)
export type CatsQuery = Static<typeof catsQuerySchema>
export const catsQueryValidator = getValidator(catsQuerySchema, queryValidator)
export const catsQueryResolver = resolve<CatsQuery, HookContext>({})
