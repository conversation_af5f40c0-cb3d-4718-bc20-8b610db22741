// TypeBox schema for teams service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const teamsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),

  // Add only_in_json properties:
  types: Type.Optional(Type.Array(Type.String())),
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  refs: Type.Optional(Type.Array(Type.String())),
  invited: Type.Optional(Type.Array(ObjectIdSchema())),
  req: Type.Optional(ObjectIdSchema()),
  contract: Type.Optional(ObjectIdSchema()),
  calendar: Type.Optional(ObjectIdSchema()),
  phone: Type.Optional(Type.String()),
  sms: Type.Optional(Type.Boolean()),
  email: Type.Optional(Type.String()),
  priority: Type.Optional(Type.Number()),
  online: Type.Optional(Type.Boolean()),

  // Remove only_in_typebox properties (commenting out):
  // org: ObjectIdSchema(), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // department: Type.Optional(Type.String()), // only_in_typebox
  // members: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // managers: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // leads: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // permissions: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // roles: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // projects: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // budget: Type.Optional(Type.Number()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Teams = Static<typeof teamsSchema>
export const teamsValidator = getValidator(teamsSchema, dataValidator)
export const teamsResolver = resolve<Teams, HookContext>({})
export const teamsExternalResolver = resolve<Teams, HookContext>({})

export const teamsDataSchema = Type.Object({
  ...Type.Omit(teamsSchema, ['_id']).properties
}, { additionalProperties: false })

export type TeamsData = Static<typeof teamsDataSchema>
export const teamsDataValidator = getValidator(teamsDataSchema, dataValidator)
export const teamsDataResolver = resolve<TeamsData, HookContext>({})

export const teamsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(teamsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  types: Type.Optional(Type.Array(Type.String())),
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  refs: Type.Optional(Type.Array(Type.String())),
  invited: Type.Optional(Type.Array(ObjectIdSchema())),
  req: Type.Optional(ObjectIdSchema()),
  contract: Type.Optional(ObjectIdSchema()),
  calendar: Type.Optional(ObjectIdSchema()),
  phone: Type.Optional(Type.String()),
  sms: Type.Optional(Type.Boolean()),
  email: Type.Optional(Type.String()),
  priority: Type.Optional(Type.Number()),
  online: Type.Optional(Type.Boolean()),

  // MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([]))
}, { additionalProperties: false })
export type TeamsPatch = Static<typeof teamsPatchSchema>
export const teamsPatchValidator = getValidator(teamsPatchSchema, dataValidator)
export const teamsPatchResolver = resolve<TeamsPatch, HookContext>({})

// Allow querying on any field from the main schema
const teamsQueryProperties = Type.Object({
  ...teamsSchema.properties,
  // Add only_in_json query properties (same as main schema):
  types: Type.Optional(Type.Array(Type.String())),
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  refs: Type.Optional(Type.Array(Type.String())),
  invited: Type.Optional(Type.Array(ObjectIdSchema())),
  req: Type.Optional(ObjectIdSchema()),
  contract: Type.Optional(ObjectIdSchema()),
  calendar: Type.Optional(ObjectIdSchema()),
  phone: Type.Optional(Type.String()),
  sms: Type.Optional(Type.Boolean()),
  email: Type.Optional(Type.String()),
  priority: Type.Optional(Type.Number()),
  online: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

export const teamsQuerySchema = querySyntax(teamsQueryProperties)
export type TeamsQuery = Static<typeof teamsQuerySchema>
export const teamsQueryValidator = getValidator(teamsQuerySchema, queryValidator)
export const teamsQueryResolver = resolve<TeamsQuery, HookContext>({})
