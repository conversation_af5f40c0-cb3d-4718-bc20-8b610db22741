// TypeBox schema for junk-drawers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const junkDrawersSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Add only_in_json properties:
  itemName: Type.Optional(Type.String()),
  itemId: Type.Optional(Type.String()),

  // Fix type mismatch: data should be unknown (not object)
  data: Type.Optional(Type.Unknown()),

  // Keep existing fields that aren't marked as only_in_typebox:
  drawer: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type JunkDrawers = Static<typeof junkDrawersSchema>
export const junkDrawersValidator = getValidator(junkDrawersSchema, dataValidator)
export const junkDrawersResolver = resolve<JunkDrawers, HookContext>({})
export const junkDrawersExternalResolver = resolve<JunkDrawers, HookContext>({})

export const junkDrawersDataSchema = Type.Object({
  ...Type.Omit(junkDrawersSchema, ['_id']).properties
}, { additionalProperties: false })

export type JunkDrawersData = Static<typeof junkDrawersDataSchema>
export const junkDrawersDataValidator = getValidator(junkDrawersDataSchema, dataValidator)
export const junkDrawersDataResolver = resolve<JunkDrawersData, HookContext>({})

export const junkDrawersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(junkDrawersSchema, ['_id'])).properties
}, { additionalProperties: false })
export type JunkDrawersPatch = Static<typeof junkDrawersPatchSchema>
export const junkDrawersPatchValidator = getValidator(junkDrawersPatchSchema, dataValidator)
export const junkDrawersPatchResolver = resolve<JunkDrawersPatch, HookContext>({})

// Allow querying on any field from the main schema
const junkDrawersQueryProperties = junkDrawersSchema
export const junkDrawersQuerySchema = querySyntax(junkDrawersQueryProperties)
export type JunkDrawersQuery = Static<typeof junkDrawersQuerySchema>
export const junkDrawersQueryValidator = getValidator(junkDrawersQuerySchema, queryValidator)
export const junkDrawersQueryResolver = resolve<JunkDrawersQuery, HookContext>({})
