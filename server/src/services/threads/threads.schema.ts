// TypeBox schema for threads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const threadsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Fix type mismatch: upVotes should be array (not string)
  upVotes: Type.Optional(Type.Array(ObjectIdSchema())),

  // Add only_in_json properties:
  downVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  voteCount: Type.Optional(Type.Number()),
  body: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  did: Type.Optional(Type.String()),
  archives: Type.Optional(Type.Array(ObjectIdSchema())),
  owners: Type.Optional(Type.Array(ObjectIdSchema())),
  parent: Type.Optional(ObjectIdSchema()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),

  // Keep existing fields that aren't marked as only_in_typebox:
  tags: Type.Optional(Type.Array(Type.String())),

  // Remove only_in_typebox properties (commenting out):
  // subject: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // participants: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // messages: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // priority: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // lastMessage: Type.Optional(Type.Any()), // only_in_typebox
  // lastActivity: Type.Optional(Type.Any()), // only_in_typebox
  // archived: Type.Optional(Type.Boolean()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Threads = Static<typeof threadsSchema>
export const threadsValidator = getValidator(threadsSchema, dataValidator)
export const threadsResolver = resolve<Threads, HookContext>({})
export const threadsExternalResolver = resolve<Threads, HookContext>({})

export const threadsDataSchema = Type.Object({
  ...Type.Omit(threadsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ThreadsData = Static<typeof threadsDataSchema>
export const threadsDataValidator = getValidator(threadsDataSchema, dataValidator)
export const threadsDataResolver = resolve<ThreadsData, HookContext>({})

export const threadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(threadsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  upVotes: Type.Optional(Type.Array(ObjectIdSchema())), // Fix type mismatch
  downVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  voteCount: Type.Optional(Type.Number()),
  body: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  did: Type.Optional(Type.String()),
  archives: Type.Optional(Type.Array(ObjectIdSchema())),
  owners: Type.Optional(Type.Array(ObjectIdSchema())),
  parent: Type.Optional(ObjectIdSchema()),
  threads: Type.Optional(Type.Array(ObjectIdSchema()))
}, { additionalProperties: false })
export type ThreadsPatch = Static<typeof threadsPatchSchema>
export const threadsPatchValidator = getValidator(threadsPatchSchema, dataValidator)
export const threadsPatchResolver = resolve<ThreadsPatch, HookContext>({})

// Allow querying on any field from the main schema
const threadsQueryProperties = Type.Object({
  ...threadsSchema.properties,
  // Add only_in_json query properties:
  _limit_to: Type.Optional(Type.Any()),
  upVotes: Type.Optional(Type.Array(ObjectIdSchema())), // Fix type mismatch
  downVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  voteCount: Type.Optional(Type.Number()),
  body: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  did: Type.Optional(Type.String()),
  archives: Type.Optional(Type.Array(ObjectIdSchema())),
  owners: Type.Optional(Type.Array(ObjectIdSchema())),
  parent: Type.Optional(ObjectIdSchema()),
  threads: Type.Optional(Type.Array(ObjectIdSchema()))
}, { additionalProperties: false })

export const threadsQuerySchema = querySyntax(threadsQueryProperties)
export type ThreadsQuery = Static<typeof threadsQuerySchema>
export const threadsQueryValidator = getValidator(threadsQuerySchema, queryValidator)
export const threadsQueryResolver = resolve<ThreadsQuery, HookContext>({})
