// TypeBox schema for ppls service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema, ServiceAddressSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const pplsSchema = Type.Object({
  _id: ObjectIdSchema(),
  firstName: Type.String(),
  lastName: Type.String(),
  email: Type.Optional(Type.String()),
  address: Type.Optional(ServiceAddressSchema),
  dateOfBirth: Type.Optional(Type.Any()),
  gender: Type.Optional(Type.String()),
  ssn: Type.Optional(Type.String()),
  login: Type.Optional(ObjectIdSchema()),
  household: Type.Optional(ObjectIdSchema()),
  active: Type.Optional(Type.Boolean()),
  name: Type.Optional(Type.String()),

  // Add only_in_json properties:
  did: Type.Optional(Type.String()),
  fid: Type.Optional(Type.String()),
  wallet: Type.Optional(Type.Record(Type.String(), Type.Any())),
  addresses: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  emails: Type.Optional(Type.Array(Type.String())),
  phones: Type.Optional(Type.Array(Type.String())),
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  online: Type.Optional(Type.Boolean()),
  onlineAt: Type.Optional(Type.Any()),
  ramp_user_id: Type.Optional(Type.String()),
  moovCardholderId: Type.Optional(Type.String()),
  ims: Type.Optional(Type.Array(ObjectIdSchema())),
  moovAccounts: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  cams: Type.Optional(Type.Array(ObjectIdSchema())),
  inGroups: Type.Optional(Type.Array(ObjectIdSchema())),
  inOrgs: Type.Optional(Type.Array(ObjectIdSchema())),
  lastGroupSync: Type.Optional(Type.Any()),
  refs: Type.Optional(Type.Array(Type.String())),
  badges: Type.Optional(Type.Array(Type.String())),
  dob: Type.Optional(Type.Any()),
  itin: Type.Optional(Type.String()),
  last4Itin: Type.Optional(Type.String()),
  last4Ssn: Type.Optional(Type.String()),
  cleanupFlag: Type.Optional(Type.Boolean()),
  enrollments: Type.Optional(Type.Array(ObjectIdSchema())),
  invites: Type.Optional(Type.Array(ObjectIdSchema())),
  preferences: Type.Optional(Type.Record(Type.String(), Type.Any())),
  card_user: Type.Optional(Type.Boolean()),
  budget_user: Type.Optional(Type.Boolean()),

  // Remove only_in_typebox properties (commenting out):
  // middleName: Type.Optional(Type.String()), // only_in_typebox
  // phone: Type.Optional(PhoneSchema), // only_in_typebox (phone.number and phone.regionCode are only_in_typebox)

  ...commonFields.properties
}, { additionalProperties: false })

export type Ppls = Static<typeof pplsSchema>
export const pplsValidator = getValidator(pplsSchema, dataValidator)
export const pplsResolver = resolve<Ppls, HookContext>({})
export const pplsExternalResolver = resolve<Ppls, HookContext>({})

export const pplsDataSchema = Type.Object({
  ...Type.Omit(pplsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PplsData = Static<typeof pplsDataSchema>
export const pplsDataValidator = getValidator(pplsDataSchema, dataValidator)
export const pplsDataResolver = resolve<PplsData, HookContext>({})

export const pplsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(pplsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  did: Type.Optional(Type.String()),
  fid: Type.Optional(Type.String()),
  wallet: Type.Optional(Type.Record(Type.String(), Type.Any())),
  addresses: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  emails: Type.Optional(Type.Array(Type.String())),
  phones: Type.Optional(Type.Array(Type.String())),
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  online: Type.Optional(Type.Boolean()),
  onlineAt: Type.Optional(Type.Any()),
  ramp_user_id: Type.Optional(Type.String()),
  moovCardholderId: Type.Optional(Type.String()),
  ims: Type.Optional(Type.Array(ObjectIdSchema())),
  moovAccounts: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  cams: Type.Optional(Type.Array(ObjectIdSchema())),
  inGroups: Type.Optional(Type.Array(ObjectIdSchema())),
  inOrgs: Type.Optional(Type.Array(ObjectIdSchema())),
  lastGroupSync: Type.Optional(Type.Any()),
  refs: Type.Optional(Type.Array(Type.String())),
  badges: Type.Optional(Type.Array(Type.String())),
  dob: Type.Optional(Type.Any()),
  itin: Type.Optional(Type.String()),
  last4Itin: Type.Optional(Type.String()),
  last4Ssn: Type.Optional(Type.String()),
  cleanupFlag: Type.Optional(Type.Boolean()),
  enrollments: Type.Optional(Type.Array(ObjectIdSchema())),
  invites: Type.Optional(Type.Array(ObjectIdSchema())),
  preferences: Type.Optional(Type.Record(Type.String(), Type.Any())),
  card_user: Type.Optional(Type.Boolean()),
  budget_user: Type.Optional(Type.Boolean()),

  // MongoDB operators using utility functions
  $addToSet: Type.Optional(addToSet([
    { path: 'cams', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'cams', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })
export type PplsPatch = Static<typeof pplsPatchSchema>
export const pplsPatchValidator = getValidator(pplsPatchSchema, dataValidator)
export const pplsPatchResolver = resolve<PplsPatch, HookContext>({})

// Allow querying on any field from the main schema
const pplsQueryProperties = Type.Object({
  ...pplsSchema.properties,
  // Add only_in_json query properties:
  _limit_to: Type.Optional(Type.Any()),
  plan_search: Type.Optional(Type.Any()),
  // Fix type mismatches for queries:
  name: Type.Optional(Type.Any()), // type_mismatch_json_unknown_typebox_string
  lastName: Type.Optional(Type.Any()), // type_mismatch_json_unknown_typebox_string
  firstName: Type.Optional(Type.Any()), // type_mismatch_json_unknown_typebox_string
  email: Type.Optional(Type.Any()), // type_mismatch_json_unknown_typebox_string
  phone: Type.Optional(Type.Any()) // type_mismatch_json_unknown_typebox_object
}, { additionalProperties: false })

export const pplsQuerySchema = querySyntax(pplsQueryProperties)
export type PplsQuery = Static<typeof pplsQuerySchema>
export const pplsQueryValidator = getValidator(pplsQuerySchema, queryValidator)
export const pplsQueryResolver = resolve<PplsQuery, HookContext>({})
