// TypeBox schema for visits service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Entered by schema with additional properties
const EnteredBySchema = Type.Object({
  login: Type.Optional(ObjectIdSchema()),
  fingerprint: Type.Optional(ObjectIdSchema()),
  at: Type.Optional(Type.Any()),
  // Add only_in_json properties for enteredBy:
  id: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  auto: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

// Main data model schema
export const visitsSchema = Type.Object({
  _id: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  person: ObjectIdSchema(),
  provider: ObjectIdSchema(),
  date: Type.Optional(Type.Any()),
  status: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  enteredBy: Type.Optional(EnteredBySchema),

  // Fix type mismatches:
  preventive: Type.Optional(Type.Boolean()), // was Type.String()
  paid: Type.Optional(Type.Record(Type.String(), Type.Any())), // was Type.Number()

  // Add only_in_json properties:
  endDate: Type.Optional(Type.Any()),
  category: Type.Optional(Type.String()),
  er: Type.Optional(Type.Boolean()),
  conditions: Type.Optional(Type.Array(Type.String())),
  claims: Type.Optional(Type.Array(ObjectIdSchema())),
  claimReqs: Type.Optional(Type.Array(ObjectIdSchema())),
  subtotal: Type.Optional(Type.Number()),
  pending: Type.Optional(Type.Number()),
  request: Type.Optional(ObjectIdSchema()),
  offer: Type.Optional(ObjectIdSchema()),
  balanceSyncedAt: Type.Optional(Type.Any()),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  files: Type.Optional(Type.Record(Type.String(), Type.Any())),

  // Keep existing fields that aren't marked as only_in_typebox:
  care: Type.Optional(Type.String()),
  plan: Type.Optional(ObjectIdSchema()),
  duration: Type.Optional(Type.Number()),
  location: Type.Optional(Type.String()),
  room: Type.Optional(Type.String()),
  vitals: Type.Optional(Type.Record(Type.String(), Type.Any())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  medications: Type.Optional(Type.Array(ObjectIdSchema())),
  followUp: Type.Optional(Type.String()),
  referrals: Type.Optional(Type.Array(ObjectIdSchema())),
  attachments: Type.Optional(Type.Array(Type.Any())),
  billing: Type.Optional(Type.Record(Type.String(), Type.Any())),
  insurance: Type.Optional(Type.Record(Type.String(), Type.Any())),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  balance: Type.Optional(Type.Number()),

  // Remove only_in_typebox properties (commenting out):
  // practitioner: Type.Optional(ObjectIdSchema()), // only_in_typebox (but practitioners array is only_in_json)
  // scheduledDate: Type.Optional(Type.Any()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // reason: Type.Optional(Type.String()), // only_in_typebox
  // diagnosis: Type.Optional(Type.String()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Visits = Static<typeof visitsSchema>
export const visitsValidator = getValidator(visitsSchema, dataValidator)
export const visitsResolver = resolve<Visits, HookContext>({})
export const visitsExternalResolver = resolve<Visits, HookContext>({})

// Schema for creating new data
export const visitsDataSchema = Type.Object({
  ...Type.Omit(visitsSchema, ['_id']).properties
}, { additionalProperties: false })

export type VisitsData = Static<typeof visitsDataSchema>
export const visitsDataValidator = getValidator(visitsDataSchema, dataValidator)
export const visitsDataResolver = resolve<VisitsData, HookContext>({})

// Schema for updating existing data
export const visitsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(visitsSchema, ['_id'])).properties,

  // MongoDB operators
  $addToSet: Type.Optional(addToSet([
    { path: 'procedures', type: ObjectIdSchema() },
    { path: 'medications', type: ObjectIdSchema() },
    { path: 'referrals', type: ObjectIdSchema() },
    { path: 'claims', type: ObjectIdSchema() },
    { path: 'claimReqs', type: ObjectIdSchema() },
    { path: 'practitioners', type: ObjectIdSchema() },
    { path: 'threads', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'procedures', type: ObjectIdSchema() },
    { path: 'medications', type: ObjectIdSchema() },
    { path: 'referrals', type: ObjectIdSchema() },
    { path: 'claims', type: ObjectIdSchema() },
    { path: 'claimReqs', type: ObjectIdSchema() },
    { path: 'practitioners', type: ObjectIdSchema() },
    { path: 'threads', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })

export type VisitsPatch = Static<typeof visitsPatchSchema>
export const visitsPatchValidator = getValidator(visitsPatchSchema, dataValidator)
export const visitsPatchResolver = resolve<VisitsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const visitsQueryProperties = Type.Object({
  ...visitsSchema.properties,
  // Add only_in_json query properties (same as main schema):
  preventive: Type.Optional(Type.Boolean()), // Fix type mismatch
  paid: Type.Optional(Type.Record(Type.String(), Type.Any())), // Fix type mismatch
  endDate: Type.Optional(Type.Any()),
  category: Type.Optional(Type.String()),
  er: Type.Optional(Type.Boolean()),
  conditions: Type.Optional(Type.Array(Type.String())),
  claims: Type.Optional(Type.Array(ObjectIdSchema())),
  claimReqs: Type.Optional(Type.Array(ObjectIdSchema())),
  subtotal: Type.Optional(Type.Number()),
  pending: Type.Optional(Type.Number()),
  request: Type.Optional(ObjectIdSchema()),
  offer: Type.Optional(ObjectIdSchema()),
  balanceSyncedAt: Type.Optional(Type.Any()),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  files: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })

export const visitsQuerySchema = querySyntax(visitsQueryProperties)

export type VisitsQuery = Static<typeof visitsQuerySchema>
export const visitsQueryValidator = getValidator(visitsQuerySchema, queryValidator)
export const visitsQueryResolver = resolve<VisitsQuery, HookContext>({})
