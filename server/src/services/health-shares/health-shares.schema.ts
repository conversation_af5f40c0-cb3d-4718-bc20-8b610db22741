// TypeBox schema for health-shares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const healthSharesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),

  // Add only_in_json properties:
  aka: Type.Optional(Type.String()),
  logo: Type.Optional(Type.Record(Type.String(), Type.Any())),
  cc_video: Type.Optional(Type.String()),
  video: Type.Optional(Type.Record(Type.String(), Type.Any())),
  files: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  products: Type.Optional(Type.Array(ObjectIdSchema())),
  financials: Type.Optional(Type.Record(Type.String(), Type.Any())),

  // Remove only_in_typebox properties (commenting out):
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type HealthShares = Static<typeof healthSharesSchema>
export const healthSharesValidator = getValidator(healthSharesSchema, dataValidator)
export const healthSharesResolver = resolve<HealthShares, HookContext>({})
export const healthSharesExternalResolver = resolve<HealthShares, HookContext>({})

export const healthSharesDataSchema = Type.Object({
  ...Type.Omit(healthSharesSchema, ['_id']).properties
}, { additionalProperties: false })

export type HealthSharesData = Static<typeof healthSharesDataSchema>
export const healthSharesDataValidator = getValidator(healthSharesDataSchema, dataValidator)
export const healthSharesDataResolver = resolve<HealthSharesData, HookContext>({})

export const healthSharesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(healthSharesSchema, ['_id'])).properties
}, { additionalProperties: false })
export type HealthSharesPatch = Static<typeof healthSharesPatchSchema>
export const healthSharesPatchValidator = getValidator(healthSharesPatchSchema, dataValidator)
export const healthSharesPatchResolver = resolve<HealthSharesPatch, HookContext>({})

// Allow querying on any field from the main schema
const healthSharesQueryProperties = Type.Object({
  ...healthSharesSchema.properties,
  // Add only_in_json query properties:
  _limit_to: Type.Optional(Type.Any()),
  // Override name to allow any type for queries (type mismatch)
  name: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const healthSharesQuerySchema = querySyntax(healthSharesQueryProperties)
export type HealthSharesQuery = Static<typeof healthSharesQuerySchema>
export const healthSharesQueryValidator = getValidator(healthSharesQuerySchema, queryValidator)
export const healthSharesQueryResolver = resolve<HealthSharesQuery, HookContext>({})
