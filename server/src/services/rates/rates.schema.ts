// TypeBox schema for rates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const ratesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Add only_in_json properties:
  state: Type.Optional(Type.String()),
  stateKey: Type.Optional(Type.String()),
  areas: Type.Optional(Type.Array(Type.String())),

  // Fix type mismatch: premium should be object (not number)
  premium: Type.Optional(Type.Record(Type.String(), Type.Any())),

  // Remove only_in_typebox properties (commenting out):
  // plan: ObjectIdSchema(), // only_in_typebox
  // coverage: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // market: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // network: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // ageRange: Type.Optional(Type.Object({...})), // only_in_typebox
  // tobacco: Type.Optional(Type.Boolean()), // only_in_typebox
  // region: Type.Optional(Type.String()), // only_in_typebox
  // county: Type.Optional(Type.String()), // only_in_typebox
  // zipCode: Type.Optional(Type.String()), // only_in_typebox
  // deductible: Type.Optional(Type.Number()), // only_in_typebox
  // outOfPocketMax: Type.Optional(Type.Number()), // only_in_typebox
  // copay: Type.Optional(Type.Number()), // only_in_typebox
  // coinsurance: Type.Optional(Type.Number()), // only_in_typebox
  // effectiveDate: Type.Optional(Type.Any()), // only_in_typebox
  // terminationDate: Type.Optional(Type.Any()), // only_in_typebox
  // rateType: Type.Optional(Type.String()), // only_in_typebox
  // familyTier: Type.Optional(Type.String()), // only_in_typebox
  // metalLevel: Type.Optional(Type.String()), // only_in_typebox
  // issuerName: Type.Optional(Type.String()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Rates = Static<typeof ratesSchema>
export const ratesValidator = getValidator(ratesSchema, dataValidator)
export const ratesResolver = resolve<Rates, HookContext>({})
export const ratesExternalResolver = resolve<Rates, HookContext>({})

export const ratesDataSchema = Type.Object({
  ...Type.Omit(ratesSchema, ['_id']).properties
}, { additionalProperties: false })

export type RatesData = Static<typeof ratesDataSchema>
export const ratesDataValidator = getValidator(ratesDataSchema, dataValidator)
export const ratesDataResolver = resolve<RatesData, HookContext>({})

export const ratesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(ratesSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  state: Type.Optional(Type.String()),
  stateKey: Type.Optional(Type.String()),
  areas: Type.Optional(Type.Array(Type.String())),
  // Fix type mismatch: premium should be object (not number)
  premium: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type RatesPatch = Static<typeof ratesPatchSchema>
export const ratesPatchValidator = getValidator(ratesPatchSchema, dataValidator)
export const ratesPatchResolver = resolve<RatesPatch, HookContext>({})

// Allow querying on any field from the main schema
const ratesQueryProperties = Type.Object({
  ...ratesSchema.properties,
  // Add only_in_json query properties (same as main schema):
  state: Type.Optional(Type.String()),
  stateKey: Type.Optional(Type.String()),
  areas: Type.Optional(Type.Array(Type.String())),
  // Fix type mismatch: premium should be object (not number)
  premium: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })

export const ratesQuerySchema = querySyntax(ratesQueryProperties)
export type RatesQuery = Static<typeof ratesQuerySchema>
export const ratesQueryValidator = getValidator(ratesQuerySchema, queryValidator)
export const ratesQueryResolver = resolve<RatesQuery, HookContext>({})
