// TypeBox schema for challenges service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const challengesSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Required fields from JSON Schema
  kind: Type.Union([Type.Literal('registration'), Type.Literal('authentication')]), // Note: enum in JSON Schema, not string

  // Properties from JSON Schema
  login: Type.Optional(ObjectIdSchema()), // may be absent for usernameless auth
  challenge: Type.Optional(Type.String()), // the raw challenge; NEVER expose externally
  connectionId: Type.Optional(Type.String()), // bind challenge to the client connection if using sockets
  expiresAt: Type.Optional(Type.Number()), // epoch ms; short TTL (e.g., 2–5 minutes)

  // Remove TypeBox-only properties that aren't in JSON Schema:
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Challenges = Static<typeof challengesSchema>
export const challengesValidator = getValidator(challengesSchema, dataValidator)
export const challengesResolver = resolve<Challenges, HookContext>({})
export const challengesExternalResolver = resolve<Challenges, HookContext>({})

export const challengesDataSchema = Type.Object({
  ...Type.Omit(challengesSchema, ['_id']).properties
}, { additionalProperties: false })

export type ChallengesData = Static<typeof challengesDataSchema>
export const challengesDataValidator = getValidator(challengesDataSchema, dataValidator)
export const challengesDataResolver = resolve<ChallengesData, HookContext>({})

export const challengesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(challengesSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ChallengesPatch = Static<typeof challengesPatchSchema>
export const challengesPatchValidator = getValidator(challengesPatchSchema, dataValidator)
export const challengesPatchResolver = resolve<ChallengesPatch, HookContext>({})

// Allow querying on any field from the main schema
const challengesQueryProperties = challengesSchema
export const challengesQuerySchema = querySyntax(challengesQueryProperties)
export type ChallengesQuery = Static<typeof challengesQuerySchema>
export const challengesQueryValidator = getValidator(challengesQuerySchema, queryValidator)
export const challengesQueryResolver = resolve<ChallengesQuery, HookContext>({})
