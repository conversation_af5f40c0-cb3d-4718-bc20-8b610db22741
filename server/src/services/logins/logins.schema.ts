// TypeBox schema for logins service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const loginsSchema = Type.Object({
  _id: ObjectIdSchema(),
  email: Type.String(),
  did: Type.Optional(Type.String()),
  password: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),

  // Fix type mismatch: phone should be string (not object)
  phone: Type.Optional(Type.String()),

  // Add only_in_json properties:
  pendingPassword: Type.Optional(Type.String()),
  ucan: Type.Optional(Type.String()),
  fingerprints: Type.Optional(Type.Array(ObjectIdSchema())),
  owner: Type.Optional(ObjectIdSchema()),
  keyPair: Type.Optional(Type.Record(Type.String(), Type.Any())),
  googleId: Type.Optional(Type.String()),
  facebookId: Type.Optional(Type.String()),
  twitterId: Type.Optional(Type.String()),
  linkedinId: Type.Optional(Type.String()),
  microsoftId: Type.Optional(Type.String()),
  githubId: Type.Optional(Type.String()),
  appleId: Type.Optional(Type.String()),
  verifyToken: Type.Optional(Type.String()),
  verifyExpires: Type.Optional(Type.Any()),
  loginAttempts: Type.Optional(Type.Number()),
  locked: Type.Optional(Type.Boolean()),
  lastLoginMethod: Type.Optional(Type.String()),
  resetExpires: Type.Optional(Type.Any()),

  // Keep existing fields that aren't marked as only_in_typebox:
  isVerified: Type.Optional(Type.Boolean()),
  lastLogin: Type.Optional(Type.Any()),
  resetToken: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // person: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // fingerprint: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // verified: Type.Optional(Type.Boolean()), // only_in_typebox
  // emailVerified: Type.Optional(Type.Boolean()), // only_in_typebox
  // phoneVerified: Type.Optional(Type.Boolean()), // only_in_typebox
  // twoFactorEnabled: Type.Optional(Type.Boolean()), // only_in_typebox
  // loginCount: Type.Optional(Type.Number()), // only_in_typebox
  // failedAttempts: Type.Optional(Type.Number()), // only_in_typebox
  // lockedUntil: Type.Optional(Type.Any()), // only_in_typebox
  // resetTokenExpires: Type.Optional(Type.Any()), // only_in_typebox
  // verificationToken: Type.Optional(Type.String()), // only_in_typebox
  // verificationTokenExpires: Type.Optional(Type.Any()), // only_in_typebox
  // roles: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // permissions: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // orgs: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // groups: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // teams: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // preferences: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // settings: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox
  // suspended: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Logins = Static<typeof loginsSchema>
export const loginsValidator = getValidator(loginsSchema, dataValidator)
export const loginsResolver = resolve<Logins, HookContext>({})
export const loginsExternalResolver = resolve<Logins, HookContext>({})

// Schema for creating new data
export const loginsDataSchema = Type.Object({
  ...Type.Omit(loginsSchema, ['_id']).properties
}, { additionalProperties: false })

export type LoginsData = Static<typeof loginsDataSchema>
export const loginsDataValidator = getValidator(loginsDataSchema, dataValidator)
export const loginsDataResolver = resolve<LoginsData, HookContext>({})

// Schema for updating existing data
export const loginsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(loginsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch:
  name: Type.Optional(Type.String()),
  pendingPassword: Type.Optional(Type.String()),
  ucan: Type.Optional(Type.String()),
  fingerprints: Type.Optional(Type.Array(ObjectIdSchema())),
  owner: Type.Optional(ObjectIdSchema()),

  // Fix type mismatch: phone should be string (not object) in patch too
  phone: Type.Optional(Type.String())
}, { additionalProperties: false })

export type LoginsPatch = Static<typeof loginsPatchSchema>
export const loginsPatchValidator = getValidator(loginsPatchSchema, dataValidator)
export const loginsPatchResolver = resolve<LoginsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const loginsQueryProperties = loginsSchema

export const loginsQuerySchema = querySyntax(loginsQueryProperties)

export type LoginsQuery = Static<typeof loginsQuerySchema>
export const loginsQueryValidator = getValidator(loginsQuerySchema, queryValidator)
export const loginsQueryResolver = resolve<LoginsQuery, HookContext>({})
