// TypeBox schema for comps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Shared schema for comps and cams (matching JSON Schema compsCamsSchema)
const ExtrasItemSchema = Type.Object({
  due: Type.Optional(Type.String()),
  awarded: Type.Optional(Type.String()),
  off: Type.Optional(Type.Boolean()),
  banks: Type.Optional(Type.Boolean()),
  type: Type.Optional(Type.Union([
    Type.Literal('percent'),
    Type.Literal('flat'),
    Type.Literal('units')
  ])),
  unit: Type.Optional(Type.Union([
    Type.Literal('hour'),
    Type.Literal('day'),
    Type.Literal('week'),
    Type.Literal('month'),
    Type.Literal('quarter'),
    Type.Literal('year'),
    Type.Literal('once')
  ])),
  amount: Type.Optional(Type.Number()),
  interval: Type.Optional(Type.Union([
    Type.Literal('hour'),
    Type.Literal('day'),
    Type.Literal('week'),
    Type.Literal('month'),
    Type.Literal('quarter'),
    Type.Literal('year'),
    Type.Literal('once')
  ])),
  terms: Type.Optional(Type.String()),
  limit: Type.Optional(Type.Number())
}, { additionalProperties: false })

export const compsCamsSchema = Type.Object({
  org: Type.Optional(ObjectIdSchema()),
  contract: Type.Optional(ObjectIdSchema()),
  interval: Type.Optional(Type.Union([
    Type.Literal('hour'),
    Type.Literal('day'),
    Type.Literal('week'),
    Type.Literal('month'),
    Type.Literal('quarter'),
    Type.Literal('year'),
    Type.Literal('once')
  ])),
  estHours: Type.Optional(Type.Number()),
  amount: Type.Optional(Type.Number()),
  terms: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  class: Type.Optional(Type.Union([Type.Literal('ee'), Type.Literal('ic')])),
  extras: Type.Optional(Type.Record(Type.String(), ExtrasItemSchema))
}, { additionalProperties: false })

export const compsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields.properties
,
  // Missing fields from old schema
  key: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Comps = Static<typeof compsSchema>
export const compsValidator = getValidator(compsSchema, dataValidator)
export const compsResolver = resolve<Comps, HookContext>({})
export const compsExternalResolver = resolve<Comps, HookContext>({})

export const compsDataSchema = Type.Object({
  ...Type.Omit(compsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CompsData = Static<typeof compsDataSchema>
export const compsDataValidator = getValidator(compsDataSchema, dataValidator)
export const compsDataResolver = resolve<CompsData, HookContext>({})

export const compsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(compsSchema, ['_id'])).properties,
  // Add only_in_json properties for patch schema:
  video: Type.Optional(Type.String()),
  references: Type.Optional(Type.Array(Type.String())),
  ad: Type.Optional(Type.String()),
  geo: Type.Optional(Type.Record(Type.String(), Type.Any())),
  stages: Type.Optional(Type.Array(Type.String())),
  contact: Type.Optional(Type.String()),
  access: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type CompsPatch = Static<typeof compsPatchSchema>
export const compsPatchValidator = getValidator(compsPatchSchema, dataValidator)
export const compsPatchResolver = resolve<CompsPatch, HookContext>({})

// Allow querying on any field from the main schema
const compsQueryProperties = compsSchema
export const compsQuerySchema = querySyntax(compsQueryProperties)
export type CompsQuery = Static<typeof compsQuerySchema>
export const compsQueryValidator = getValidator(compsQuerySchema, queryValidator)
export const compsQueryResolver = resolve<CompsQuery, HookContext>({})
