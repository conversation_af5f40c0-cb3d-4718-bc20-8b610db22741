// TypeBox schema for claim-payments service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, ImageSchema } from '../../utils/common/typebox-schemas.js'

// Refund schema (matching JSON Schema version)
const RefundSchema = Type.Object({
  ded: Type.Optional(Type.Number()),
  coins: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  amount: Type.Number(),
  refundedAt: Type.Optional(Type.Any()),
  status: Type.Optional(Type.Union([
    Type.Literal('pending'),
    Type.Literal('complete'),
    Type.Literal('cancelled')
  ])),
  confirmation: Type.String()
}, { additionalProperties: false })

export const claimPaymentsSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Required fields from JSON Schema
  claim: ObjectIdSchema(),
  amount: Type.Number(),
  org: ObjectIdSchema(),
  person: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  provider: ObjectIdSchema(),

  // Properties from JSON Schema
  visit: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  preventive: Type.Optional(Type.Boolean()), // Note: Boolean in JSON Schema, not String
  allowedMethods: Type.Optional(Type.Array(Type.Union([
    Type.Literal('card'),
    Type.Literal('ach'),
    Type.Literal('ca')
  ]))),
  method: Type.Optional(Type.Union([
    Type.Literal('card'),
    Type.Literal('ach'),
    Type.Literal('ca'),
    Type.Literal('ext')
  ])),
  checkoutSession: Type.Optional(Type.String()),
  customerId: Type.Optional(Type.String()),
  payment_method: Type.Optional(Type.String()),
  transactionId: Type.Optional(Type.String()),
  providerCareAccount: Type.Optional(ObjectIdSchema()),
  careAccount: Type.Optional(ObjectIdSchema()),
  card: Type.Optional(ObjectIdSchema()),
  budget: Type.Optional(ObjectIdSchema()),
  ded: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coins: Type.Optional(Type.Number()),
  due: Type.Optional(Type.String()),
  refunds: Type.Optional(Type.Array(RefundSchema)),
  type: Type.Optional(Type.String()),
  confirmation: Type.Optional(Type.String()),
  confirmedAt: Type.Optional(Type.Any()),
  fullPayment: Type.Optional(Type.Boolean()),
  status: Type.Optional(Type.Union([
    Type.Literal('request'),
    Type.Literal('offer'),
    Type.Literal('pending'),
    Type.Literal('paid'),
    Type.Literal('cancelled'),
    Type.Literal('refunded'),
    Type.Literal('returned')
  ])),
  files: Type.Optional(Type.Record(Type.String(), ImageSchema)),

  // Remove TypeBox-only properties that aren't in JSON Schema:
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type ClaimPayments = Static<typeof claimPaymentsSchema>
export const claimPaymentsValidator = getValidator(claimPaymentsSchema, dataValidator)
export const claimPaymentsResolver = resolve<ClaimPayments, HookContext>({
  status: async (val) => {
    if (val === 'paid') return 'pending'
    return val;
  }
})
export const claimPaymentsExternalResolver = resolve<ClaimPayments, HookContext>({})

export const claimPaymentsDataSchema = Type.Object({
  ...Type.Omit(claimPaymentsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ClaimPaymentsData = Static<typeof claimPaymentsDataSchema>
export const claimPaymentsDataValidator = getValidator(claimPaymentsDataSchema, dataValidator)
export const claimPaymentsDataResolver = resolve<ClaimPaymentsData, HookContext>({})

// Push/pull operations (matching JSON Schema approach)
const pushPullOpts = [
  { path: 'allowedMethods', type: Type.Union([Type.Literal('card'), Type.Literal('ach'), Type.Literal('ca')]) },
  { path: 'refunds', type: RefundSchema }
]

export const claimPaymentsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(claimPaymentsSchema, ['_id'])).properties,
  // MongoDB operators using utility functions (matching JSON Schema approach)
  $addToSet: Type.Optional(addToSet(pushPullOpts)),
  $pull: Type.Optional(pull(pushPullOpts))
}, { additionalProperties: false })

export type ClaimPaymentsPatch = Static<typeof claimPaymentsPatchSchema>
export const claimPaymentsPatchValidator = getValidator(claimPaymentsPatchSchema, dataValidator)
export const claimPaymentsPatchResolver = resolve<ClaimPaymentsPatch, HookContext>({})

// Allow querying on any field from the main schema
const claimPaymentsQueryProperties = claimPaymentsSchema
export const claimPaymentsQuerySchema = querySyntax(claimPaymentsQueryProperties)
export type ClaimPaymentsQuery = Static<typeof claimPaymentsQuerySchema>
export const claimPaymentsQueryValidator = getValidator(claimPaymentsQuerySchema, queryValidator)
export const claimPaymentsQueryResolver = resolve<ClaimPaymentsQuery, HookContext>({})
