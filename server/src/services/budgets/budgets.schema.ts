// TypeBox schema for budgets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Last sync schema (matching JSON Schema version)
const LastSyncSchema = Type.Object({
  adjusted: Type.Optional(Type.Boolean()),
  balance: Type.Optional(Type.Number()),
  date: Type.Optional(Type.Any()),
  err: Type.Optional(Type.String()),
  amount: Type.Optional(Type.Number()),
  recurs: Type.Optional(Type.Number()),
  adjust_spent: Type.Optional(Type.Number()),
  adjust_spent_pending: Type.Optional(Type.Number()),
  adjust_amount: Type.Optional(Type.Number()),
  adjust_assigned_amount: Type.Optional(Type.Number()),
  adjust_recurs: Type.Optional(Type.Number()),
  adjust_assigned_recurs: Type.Optional(Type.Number()),
  freeze: Type.Optional(Type.Boolean()),
  excess: Type.Optional(Type.Number()),
  by: Type.Optional(ObjectIdSchema())
}, { additionalProperties: false })

// Prior auth schema (matching JSON Schema version)
const PriorAuthSchema = Type.Object({
  min: Type.Optional(Type.Number()), // smallest transaction requiring approval
  max: Type.Optional(Type.Number())  // largest allowable transaction
}, { additionalProperties: false })

// Prior months schema (matching JSON Schema version)
const PriorMonthsSchema = Type.Object({
  amount: Type.Optional(Type.Number()),
  recurs: Type.Optional(Type.Number()),
  parent: Type.Optional(ObjectIdSchema()),
  careAccount: Type.Optional(ObjectIdSchema()),
  spent_sub: Type.Optional(Type.Number()),
  spent_pending: Type.Optional(Type.Number()),
  spent_pending_sub: Type.Optional(Type.Number()),
  spent: Type.Optional(Type.Number())
}, { additionalProperties: false })

export const budgetsSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Required fields from JSON Schema
  owner: ObjectIdSchema(),
  name: Type.String(),
  moov_id: Type.String(),

  // All other properties from JSON Schema
  amount: Type.Optional(Type.Number()),
  approvers: Type.Optional(Type.Array(ObjectIdSchema())),
  assigned_amount: Type.Optional(Type.Number()),
  assigned_recurs: Type.Optional(Type.Number()),
  expenses: Type.Optional(Type.Array(ObjectIdSchema())),
  careAccount: Type.Optional(ObjectIdSchema()),
  category: Type.Optional(Type.String()),
  children: Type.Optional(Type.Array(ObjectIdSchema())),
  connect_id: Type.Optional(Type.String()),
  lastInc: Type.Optional(Type.String()),
  lastSync: Type.Optional(LastSyncSchema),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  ramp_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_blacklist: Type.Optional(Type.Array(Type.String())),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  parent: Type.Optional(ObjectIdSchema()),
  priorAuth: Type.Optional(PriorAuthSchema),
  recurs: Type.Optional(Type.Number()),
  runSync: Type.Optional(Type.String()),
  spent: Type.Optional(Type.Number()),
  spent_pending: Type.Optional(Type.Number()),
  spent_pending_sub: Type.Optional(Type.Number()),
  spent_sub: Type.Optional(Type.Number()),
  syncHistory: Type.Optional(Type.Array(LastSyncSchema)),
  rampSpendProgram: Type.Optional(Type.String()),
  priorMonths: Type.Optional(Type.Array(PriorMonthsSchema)),

  // Remove the TypeBox-only fields that aren't in JSON Schema
  // account: ObjectIdSchema(), // This was only_in_typebox
  // period: Type.Optional(Type.String()), // This was only_in_typebox
  // active: Type.Optional(Type.Boolean()), // This was only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Budgets = Static<typeof budgetsSchema>
export const budgetsValidator = getValidator(budgetsSchema, dataValidator)
export const budgetsResolver = resolve<Budgets, HookContext>({})
export const budgetsExternalResolver = resolve<Budgets, HookContext>({})

export const budgetsDataSchema = Type.Object({
  ...Type.Omit(budgetsSchema, ['_id']).properties
}, { additionalProperties: false })

export type BudgetsData = Static<typeof budgetsDataSchema>
export const budgetsDataValidator = getValidator(budgetsDataSchema, dataValidator)
export const budgetsDataResolver = resolve<BudgetsData, HookContext>({})

export const budgetsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(budgetsSchema, ['_id'])).properties,
  // MongoDB operators using utility functions (matching JSON Schema approach)
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $addToSet: Type.Optional(addToSet([
    { path: 'syncHistory', type: LastSyncSchema },
    { path: 'mcc_whitelist', type: Type.String() },
    { path: 'mcc_blacklist', type: Type.String() },
    { path: 'budgets', type: ObjectIdSchema() },
    { path: 'cards', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'syncHistory', type: LastSyncSchema },
    { path: 'mcc_whitelist', type: Type.String() },
    { path: 'mcc_blacklist', type: Type.String() },
    { path: 'budgets', type: ObjectIdSchema() },
    { path: 'cards', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })
export type BudgetsPatch = Static<typeof budgetsPatchSchema>
export const budgetsPatchValidator = getValidator(budgetsPatchSchema, dataValidator)
export const budgetsPatchResolver = resolve<BudgetsPatch, HookContext>({})

// Allow querying on any field from the main schema
const budgetsQueryProperties = Type.Object({
  ...budgetsSchema.properties,
  // Query-specific properties
  _limit_to: Type.Optional(Type.Any()),
  // Override name to allow any type for queries (matching JSON Schema)
  name: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const budgetsQuerySchema = querySyntax(budgetsQueryProperties)
export type BudgetsQuery = Static<typeof budgetsQuerySchema>
export const budgetsQueryValidator = getValidator(budgetsQuerySchema, queryValidator)
export const budgetsQueryResolver = resolve<BudgetsQuery, HookContext>({})
