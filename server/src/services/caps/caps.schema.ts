// TypeBox schema for caps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Caps item schema (for pattern properties)
const CapsItemSchema = Type.Object({
  description: Type.Optional(Type.String()),
  ucan: Type.Optional(Type.String()),
  logins: Type.Optional(Type.Array(ObjectIdSchema()))
}, { additionalProperties: false })

export const capsSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Required fields from JSON Schema
  subject: ObjectIdSchema(),
  did: Type.String(),

  // Properties from JSON Schema
  subjectService: Type.Optional(Type.String()),
  caps: Type.Optional(Type.Record(Type.String(), CapsItemSchema)),

  // Remove TypeBox-only properties that aren't in JSON Schema:
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // limit: Type.Optional(Type.Number()), // only_in_typebox
  // used: Type.Optional(Type.Number()), // only_in_typebox
  // remaining: Type.Optional(Type.Number()), // only_in_typebox
  // period: Type.Optional(Type.String()), // only_in_typebox
  // resetDate: Type.Optional(Type.Any()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Caps = Static<typeof capsSchema>
export const capsValidator = getValidator(capsSchema, dataValidator)
export const capsResolver = resolve<Caps, HookContext>({})
export const capsExternalResolver = resolve<Caps, HookContext>({})

export const capsDataSchema = Type.Object({
  ...Type.Omit(capsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CapsData = Static<typeof capsDataSchema>
export const capsDataValidator = getValidator(capsDataSchema, dataValidator)
export const capsDataResolver = resolve<CapsData, HookContext>({})

export const capsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(capsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type CapsPatch = Static<typeof capsPatchSchema>
export const capsPatchValidator = getValidator(capsPatchSchema, dataValidator)
export const capsPatchResolver = resolve<CapsPatch, HookContext>({})

// Allow querying on any field from the main schema
const capsQueryProperties = Type.Object({
  ...capsSchema.properties,
  // Query-specific properties
  _limit_to: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const capsQuerySchema = querySyntax(capsQueryProperties)
export type CapsQuery = Static<typeof capsQuerySchema>
export const capsQueryValidator = getValidator(capsQuerySchema, queryValidator)
export const capsQueryResolver = resolve<CapsQuery, HookContext>({})
