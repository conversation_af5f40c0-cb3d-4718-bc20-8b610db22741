// TypeBox schema for expenses service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const expensesSchema = Type.Object({
  _id: ObjectIdSchema(),
  amount: Type.Number(),

  // Add only_in_json properties:
  max_transaction: Type.Optional(Type.Number()),
  recurs: Type.Optional(Type.Number()),
  recur_start: Type.Optional(Type.Any()),
  next_reset: Type.Optional(Type.Any()),
  lock_date: Type.Optional(Type.Any()),
  spent: Type.Optional(Type.Number()),
  spent_pending: Type.Optional(Type.Number()),
  singleUse: Type.Optional(Type.Boolean()),
  singleVendor: Type.Optional(Type.Boolean()),
  perTransactionLimit: Type.Optional(Type.Number()),
  approvers: Type.Optional(Type.Array(ObjectIdSchema())),
  budget: Type.Optional(ObjectIdSchema()),
  limit_owner: Type.Optional(ObjectIdSchema()),
  closedLimitIds: Type.Optional(Type.Array(Type.String())),
  closedLimits: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  last4: Type.Optional(Type.String()),
  lastSync: Type.Optional(Type.Record(Type.String(), Type.Any())),
  ramp_limit: Type.Optional(Type.String()),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  ramp_whitelist: Type.Optional(Type.Array(Type.String())),
  ramp_blacklist: Type.Optional(Type.Array(Type.String())),
  vendor_whitelist: Type.Optional(Type.Array(Type.String())),
  vendor_blacklist: Type.Optional(Type.Array(Type.String())),
  mcc_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_blacklist: Type.Optional(Type.Array(Type.String())),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  name: Type.Optional(Type.String()),
  owner: Type.Optional(ObjectIdSchema()),
  preAuth: Type.Optional(Type.Record(Type.String(), Type.Any())),
  status: Type.Optional(Type.String()),
  users: Type.Optional(Type.Array(ObjectIdSchema())),

  // Remove only_in_typebox properties (commenting out):
  // account: ObjectIdSchema(), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // vendor: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // date: Type.Optional(Type.Any()), // only_in_typebox
  // receipt: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // approved: Type.Optional(Type.Boolean()), // only_in_typebox
  // approvedBy: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // reimbursed: Type.Optional(Type.Boolean()), // only_in_typebox
  // reimbursedAt: Type.Optional(Type.Any()), // only_in_typebox
  // tags: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Expenses = Static<typeof expensesSchema>
export const expensesValidator = getValidator(expensesSchema, dataValidator)
export const expensesResolver = resolve<Expenses, HookContext>({})
export const expensesExternalResolver = resolve<Expenses, HookContext>({})

export const expensesDataSchema = Type.Object({
  ...Type.Omit(expensesSchema, ['_id']).properties
}, { additionalProperties: false })

export type ExpensesData = Static<typeof expensesDataSchema>
export const expensesDataValidator = getValidator(expensesDataSchema, dataValidator)
export const expensesDataResolver = resolve<ExpensesData, HookContext>({})

export const expensesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(expensesSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
}, { additionalProperties: false })
export type ExpensesPatch = Static<typeof expensesPatchSchema>
export const expensesPatchValidator = getValidator(expensesPatchSchema, dataValidator)
export const expensesPatchResolver = resolve<ExpensesPatch, HookContext>({})

// Allow querying on any field from the main schema
const expensesQueryProperties = expensesSchema
export const expensesQuerySchema = querySyntax(expensesQueryProperties)
export type ExpensesQuery = Static<typeof expensesQuerySchema>
export const expensesQueryValidator = getValidator(expensesQuerySchema, queryValidator)
export const expensesQueryResolver = resolve<ExpensesQuery, HookContext>({})
