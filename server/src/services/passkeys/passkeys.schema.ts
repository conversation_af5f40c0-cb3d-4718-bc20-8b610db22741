// TypeBox schema for passkeys service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const passkeysSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Add only_in_json properties:
  credentialId: Type.Optional(Type.String()),
  publicKey: Type.Optional(Type.String()),
  signCount: Type.Optional(Type.Number()),
  transports: Type.Optional(Type.Array(Type.String())),
  aaguid: Type.Optional(Type.String()),
  backupEligible: Type.Optional(Type.Boolean()),
  backupState: Type.Optional(Type.Boolean()),
  displayName: Type.Optional(Type.String()),

  // Keep existing fields that aren't marked as only_in_typebox:
  login: Type.Optional(Type.String()),
  rpID: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Passkeys = Static<typeof passkeysSchema>
export const passkeysValidator = getValidator(passkeysSchema, dataValidator)
export const passkeysResolver = resolve<Passkeys, HookContext>({})
export const passkeysExternalResolver = resolve<Passkeys, HookContext>({})

export const passkeysDataSchema = Type.Object({
  ...Type.Omit(passkeysSchema, ['_id']).properties
}, { additionalProperties: false })

export type PasskeysData = Static<typeof passkeysDataSchema>
export const passkeysDataValidator = getValidator(passkeysDataSchema, dataValidator)
export const passkeysDataResolver = resolve<PasskeysData, HookContext>({})

export const passkeysPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(passkeysSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  credentialId: Type.Optional(Type.String()),
  publicKey: Type.Optional(Type.String()),
  signCount: Type.Optional(Type.Number()),
  transports: Type.Optional(Type.Array(Type.String())),
  aaguid: Type.Optional(Type.String()),
  backupEligible: Type.Optional(Type.Boolean()),
  backupState: Type.Optional(Type.Boolean()),
  displayName: Type.Optional(Type.String())
}, { additionalProperties: false })
export type PasskeysPatch = Static<typeof passkeysPatchSchema>
export const passkeysPatchValidator = getValidator(passkeysPatchSchema, dataValidator)
export const passkeysPatchResolver = resolve<PasskeysPatch, HookContext>({})

// Allow querying on any field from the main schema
const passkeysQueryProperties = Type.Object({
  ...passkeysSchema.properties,
  // Add only_in_json query properties (same as main schema):
  credentialId: Type.Optional(Type.String()),
  publicKey: Type.Optional(Type.String()),
  signCount: Type.Optional(Type.Number()),
  transports: Type.Optional(Type.Array(Type.String())),
  aaguid: Type.Optional(Type.String()),
  backupEligible: Type.Optional(Type.Boolean()),
  backupState: Type.Optional(Type.Boolean()),
  displayName: Type.Optional(Type.String())
}, { additionalProperties: false })

export const passkeysQuerySchema = querySyntax(passkeysQueryProperties)
export type PasskeysQuery = Static<typeof passkeysQuerySchema>
export const passkeysQueryValidator = getValidator(passkeysQuerySchema, queryValidator)
export const passkeysQueryResolver = resolve<PasskeysQuery, HookContext>({})
