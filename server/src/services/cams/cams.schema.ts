// TypeBox schema for cams service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'
import { compsCamsSchema } from '../comps/comps.schema.js'

export const camsSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Required fields from JSON Schema
  person: ObjectIdSchema(),

  // Cams-specific properties from JSON Schema
  comp: Type.Optional(ObjectIdSchema()),
  hireDate: Type.Optional(Type.Any()),
  hoursWorked: Type.Optional(Type.Number()),
  off: Type.Optional(Type.Boolean()),
  stage: Type.Optional(Type.String()),
  active: Type.Optional(Type.String()), // Note: String in JSON Schema, not Boolean
  group: Type.Optional(ObjectIdSchema()),
  terminated: Type.Optional(Type.Boolean()),
  terminatedAt: Type.Optional(Type.Any()),
  terminatedBy: Type.Optional(ObjectIdSchema()),

  // Shared properties from compsCamsSchema
  ...compsCamsSchema.properties,

  // Remove TypeBox-only properties that aren't in JSON Schema:
  // name: Type.Optional(Type.String()), // This comes from compsCamsSchema
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // url: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // settings: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Cams = Static<typeof camsSchema>
export const camsValidator = getValidator(camsSchema, dataValidator)
export const camsResolver = resolve<Cams, HookContext>({})
export const camsExternalResolver = resolve<Cams, HookContext>({})

export const camsDataSchema = Type.Object({
  ...Type.Omit(camsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CamsData = Static<typeof camsDataSchema>
export const camsDataValidator = getValidator(camsDataSchema, dataValidator)
export const camsDataResolver = resolve<CamsData, HookContext>({})

export const camsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(camsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
}, { additionalProperties: false })
export type CamsPatch = Static<typeof camsPatchSchema>
export const camsPatchValidator = getValidator(camsPatchSchema, dataValidator)
export const camsPatchResolver = resolve<CamsPatch, HookContext>({})

// Allow querying on any field from the main schema
const camsQueryProperties = Type.Object({
  ...camsSchema.properties,
  // Query-specific properties
  _limit_to: Type.Optional(Type.Any()),
  // Override name to allow any type for queries (matching JSON Schema)
  name: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const camsQuerySchema = querySyntax(camsQueryProperties)
export type CamsQuery = Static<typeof camsQuerySchema>
export const camsQueryValidator = getValidator(camsQuerySchema, queryValidator)
export const camsQueryResolver = resolve<CamsQuery, HookContext>({})
