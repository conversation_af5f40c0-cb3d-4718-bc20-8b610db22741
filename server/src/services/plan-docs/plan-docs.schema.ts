// TypeBox schema for plan-docs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const planDocsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),

  // Keep existing fields that aren't marked as only_in_typebox:
  plan: Type.Optional(ObjectIdSchema()),

  // Fix type mismatch: smb should be boolean (not string)
  smb: Type.Optional(Type.Boolean()),

  // Add only_in_json properties:
  public: Type.Optional(Type.Boolean()),
  printCount: Type.Optional(Type.Number()),
  class: Type.Optional(Type.String()),
  subClass: Type.Optional(Type.String()),
  path: Type.Optional(Type.String()),
  sectionsUpdatedAt: Type.Optional(Type.Any()),
  template: Type.Optional(ObjectIdSchema()),
  sections: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),

  // Remove only_in_typebox properties (commenting out):
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type PlanDocs = Static<typeof planDocsSchema>
export const planDocsValidator = getValidator(planDocsSchema, dataValidator)
export const planDocsResolver = resolve<PlanDocs, HookContext>({})
export const planDocsExternalResolver = resolve<PlanDocs, HookContext>({})

export const planDocsDataSchema = Type.Object({
  ...Type.Omit(planDocsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PlanDocsData = Static<typeof planDocsDataSchema>
export const planDocsDataValidator = getValidator(planDocsDataSchema, dataValidator)
export const planDocsDataResolver = resolve<PlanDocsData, HookContext>({})

export const planDocsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(planDocsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  smb: Type.Optional(Type.Boolean()), // Fix type mismatch
  public: Type.Optional(Type.Boolean()),
  printCount: Type.Optional(Type.Number()),
  class: Type.Optional(Type.String()),
  subClass: Type.Optional(Type.String()),
  path: Type.Optional(Type.String()),
  sectionsUpdatedAt: Type.Optional(Type.Any()),
  template: Type.Optional(ObjectIdSchema()),
  sections: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any())))
}, { additionalProperties: false })
export type PlanDocsPatch = Static<typeof planDocsPatchSchema>
export const planDocsPatchValidator = getValidator(planDocsPatchSchema, dataValidator)
export const planDocsPatchResolver = resolve<PlanDocsPatch, HookContext>({})

// Allow querying on any field from the main schema
const planDocsQueryProperties = planDocsSchema
export const planDocsQuerySchema = querySyntax(planDocsQueryProperties)
export type PlanDocsQuery = Static<typeof planDocsQuerySchema>
export const planDocsQueryValidator = getValidator(planDocsQuerySchema, queryValidator)
export const planDocsQueryResolver = resolve<PlanDocsQuery, HookContext>({})
