// TypeBox schema for meds service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const medsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  rxcui: Type.Optional(Type.String()),

  // Add only_in_json properties:
  rxcuis: Type.Optional(Type.Array(Type.String())),
  s_f: Type.Optional(Type.String()),
  variants: Type.Optional(Type.Array(Type.String())),
  medical_name: Type.Optional(Type.String()),
  consumer_name: Type.Optional(Type.String()),
  activeIngredient: Type.Optional(Type.String()),
  synonyms: Type.Optional(Type.Array(Type.String())),
  sbdOf: Type.Optional(Type.String()),
  ndcs: Type.Optional(Type.Array(Type.String())),
  info: Type.Optional(Type.Record(Type.String(), Type.Any())),

  // Keep existing fields that aren't marked as only_in_typebox:
  standard: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // genericName: Type.Optional(Type.String()), // only_in_typebox
  // brandName: Type.Optional(Type.String()), // only_in_typebox
  // ndc: Type.Optional(Type.String()), // only_in_typebox
  // strength: Type.Optional(Type.String()), // only_in_typebox
  // dosageForm: Type.Optional(Type.String()), // only_in_typebox
  // route: Type.Optional(Type.String()), // only_in_typebox
  // manufacturer: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // class: Type.Optional(Type.String()), // only_in_typebox
  // controlled: Type.Optional(Type.Boolean()), // only_in_typebox
  // schedule: Type.Optional(Type.String()), // only_in_typebox
  // indications: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // contraindications: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // sideEffects: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // interactions: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Meds = Static<typeof medsSchema>
export const medsValidator = getValidator(medsSchema, dataValidator)
export const medsResolver = resolve<Meds, HookContext>({})
export const medsExternalResolver = resolve<Meds, HookContext>({})

export const medsDataSchema = Type.Object({
  ...Type.Omit(medsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MedsData = Static<typeof medsDataSchema>
export const medsDataValidator = getValidator(medsDataSchema, dataValidator)
export const medsDataResolver = resolve<MedsData, HookContext>({})

export const medsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(medsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type MedsPatch = Static<typeof medsPatchSchema>
export const medsPatchValidator = getValidator(medsPatchSchema, dataValidator)
export const medsPatchResolver = resolve<MedsPatch, HookContext>({})

// Allow querying on any field from the main schema
const medsQueryProperties = medsSchema
export const medsQuerySchema = querySyntax(medsQueryProperties)
export type MedsQuery = Static<typeof medsQuerySchema>
export const medsQueryValidator = getValidator(medsQuerySchema, queryValidator)
export const medsQueryResolver = resolve<MedsQuery, HookContext>({})
