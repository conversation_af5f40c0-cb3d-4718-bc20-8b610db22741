// TypeBox schema for hosts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const hostsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Fix type mismatch: appDefault should be boolean (not string)
  appDefault: Type.Optional(Type.Boolean()),

  // Add only_in_json properties:
  dba: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  org: Type.Optional(ObjectIdSchema()),
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  subdomain: Type.Optional(Type.String()),
  allVideos: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  phones: Type.Optional(Type.Array(Type.String())),
  emails: Type.Optional(Type.Array(Type.String())),
  locations: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  refs: Type.Optional(Type.Array(Type.String())),
  teams: Type.Optional(Type.Array(ObjectIdSchema())),
  npn: Type.Optional(Type.String()),
  shopStatuses: Type.Optional(Type.Array(Type.String())),
  publicSupport: Type.Optional(Type.Boolean()),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  videos: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  states: Type.Optional(Type.Array(Type.String())),
  roles: Type.Optional(Type.Array(Type.String())),
  broker: Type.Optional(Type.Boolean()),

  // Remove only_in_typebox properties (commenting out):
  // name: Type.Optional(Type.String()), // only_in_typebox
  // hostname: Type.Optional(Type.String()), // only_in_typebox
  // ip: Type.Optional(Type.String()), // only_in_typebox
  // port: Type.Optional(Type.Number()), // only_in_typebox
  // protocol: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // lastCheck: Type.Optional(Type.Any()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Hosts = Static<typeof hostsSchema>
export const hostsValidator = getValidator(hostsSchema, dataValidator)
export const hostsResolver = resolve<Hosts, HookContext>({})
export const hostsExternalResolver = resolve<Hosts, HookContext>({})

export const hostsDataSchema = Type.Object({
  ...Type.Omit(hostsSchema, ['_id']).properties
}, { additionalProperties: false })

export type HostsData = Static<typeof hostsDataSchema>
export const hostsDataValidator = getValidator(hostsDataSchema, dataValidator)
export const hostsDataResolver = resolve<HostsData, HookContext>({})

export const hostsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(hostsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type HostsPatch = Static<typeof hostsPatchSchema>
export const hostsPatchValidator = getValidator(hostsPatchSchema, dataValidator)
export const hostsPatchResolver = resolve<HostsPatch, HookContext>({})

// Allow querying on any field from the main schema
const hostsQueryProperties = hostsSchema
export const hostsQuerySchema = querySyntax(hostsQueryProperties)
export type HostsQuery = Static<typeof hostsQuerySchema>
export const hostsQueryValidator = getValidator(hostsQuerySchema, queryValidator)
export const hostsQueryResolver = resolve<HostsQuery, HookContext>({})
