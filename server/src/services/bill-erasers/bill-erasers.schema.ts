// TypeBox schema for bill-erasers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, ImageSchema, MandateSchema } from '../../utils/common/typebox-schemas.js'

export const billErasersSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields
  person: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  provider: ObjectIdSchema(),
  session: Type.String(),

  // Optional fields from JSON schema
  rx: Type.Optional(Type.Boolean()),
  prefContact: Type.Optional(Type.Union([Type.Literal('phone'), Type.Literal('email')])),
  providerName: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  zip: Type.Optional(Type.String()),
  msa: Type.Optional(Type.String()),
  income: Type.Optional(Type.Number()),
  hhCount: Type.Optional(Type.Number()),
  carrier: Type.Optional(Type.String()),
  assignedTo: Type.Optional(Type.Array(ObjectIdSchema())),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(Type.Union([
    Type.Literal(1), Type.Literal(2), Type.Literal(3),
    Type.Literal(4), Type.Literal(5), Type.Literal(6)
  ])),
  disposition: Type.Optional(Type.Union([
    Type.Literal(1), Type.Literal(2), Type.Literal(3),
    Type.Literal(4), Type.Literal(5)
  ])),
  files: Type.Optional(Type.Array(ImageSchema)),
  savePercent: Type.Optional(Type.Number()),
  mandate: Type.Optional(MandateSchema),
  personRelationship: Type.Optional(Type.String()),
  hipaa: Type.Optional(Type.String()),
  signature: Type.Optional(Type.String()),
  original_price: Type.Optional(Type.Number()),
  est_price: Type.Optional(Type.Number()),

  // PatternProperties for prices - converted to TypeBox Record
  prices: Type.Optional(Type.Record(Type.String(), Type.Object({
    price: ObjectIdSchema(),
    rev_code: Type.String(),
    status: Type.Union([Type.Literal('data'), Type.Literal('confirmed')]),
    threads: Type.Array(ObjectIdSchema())
  }))),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type BillErasers = Static<typeof billErasersSchema>
export const billErasersValidator = getValidator(billErasersSchema, dataValidator)
export const billErasersResolver = resolve<BillErasers, HookContext>({})
export const billErasersExternalResolver = resolve<BillErasers, HookContext>({})

export const billErasersDataSchema = Type.Object({
  ...Type.Omit(billErasersSchema, ['_id']).properties
}, { additionalProperties: false })

export type BillErasersData = Static<typeof billErasersDataSchema>
export const billErasersDataValidator = getValidator(billErasersDataSchema, dataValidator)
export const billErasersDataResolver = resolve<BillErasersData, HookContext>({})

export const billErasersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(billErasersSchema, ['_id'])).properties,

  // MongoDB operators for array fields
  $push: Type.Optional(Type.Object({
    files: Type.Optional(ImageSchema),
    assignedTo: Type.Optional(ObjectIdSchema()),
    threads: Type.Optional(ObjectIdSchema())
  })),
  $addToSet: Type.Optional(Type.Object({
    files: Type.Optional(ImageSchema),
    assignedTo: Type.Optional(ObjectIdSchema()),
    threads: Type.Optional(ObjectIdSchema())
  })),
  $pull: Type.Optional(Type.Object({
    files: Type.Optional(ImageSchema),
    assignedTo: Type.Optional(ObjectIdSchema()),
    threads: Type.Optional(ObjectIdSchema())
  }))
}, { additionalProperties: false })
export type BillErasersPatch = Static<typeof billErasersPatchSchema>
export const billErasersPatchValidator = getValidator(billErasersPatchSchema, dataValidator)
export const billErasersPatchResolver = resolve<BillErasersPatch, HookContext>({})

// Allow querying on any field from the main schema
const billErasersQueryProperties = billErasersSchema
export const billErasersQuerySchema = querySyntax(billErasersQueryProperties)
export type BillErasersQuery = Static<typeof billErasersQuerySchema>
export const billErasersQueryValidator = getValidator(billErasersQuerySchema, queryValidator)
export const billErasersQueryResolver = resolve<BillErasersQuery, HookContext>({})
