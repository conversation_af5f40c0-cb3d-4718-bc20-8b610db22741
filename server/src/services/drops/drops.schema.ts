// TypeBox schema for drops service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const dropsSchema = Type.Object({
  _id: ObjectIdSchema(),
  type: Type.Optional(Type.String()),

  // Add only_in_json properties:
  archives: Type.Optional(Type.Array(ObjectIdSchema())),
  topAnswer: Type.Optional(ObjectIdSchema()),
  topScore: Type.Optional(Type.Number()),
  title: Type.Optional(Type.String()),
  body: Type.Optional(Type.String()),
  class: Type.Optional(Type.String()),
  anonymous: Type.Optional(Type.Boolean()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  voteCount: Type.Optional(Type.Number()),
  upVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  downVotes: Type.Optional(Type.Array(ObjectIdSchema())),

  // Keep existing fields that aren't marked as only_in_typebox:
  tags: Type.Optional(Type.Array(Type.String())),

  // Remove only_in_typebox properties (commenting out):
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Drops = Static<typeof dropsSchema>
export const dropsValidator = getValidator(dropsSchema, dataValidator)
export const dropsResolver = resolve<Drops, HookContext>({})
export const dropsExternalResolver = resolve<Drops, HookContext>({})

export const dropsDataSchema = Type.Object({
  ...Type.Omit(dropsSchema, ['_id']).properties
}, { additionalProperties: false })

export type DropsData = Static<typeof dropsDataSchema>
export const dropsDataValidator = getValidator(dropsDataSchema, dataValidator)
export const dropsDataResolver = resolve<DropsData, HookContext>({})

export const dropsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(dropsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type DropsPatch = Static<typeof dropsPatchSchema>
export const dropsPatchValidator = getValidator(dropsPatchSchema, dataValidator)
export const dropsPatchResolver = resolve<DropsPatch, HookContext>({})

// Allow querying on any field from the main schema
const dropsQueryProperties = dropsSchema
export const dropsQuerySchema = querySyntax(dropsQueryProperties)
export type DropsQuery = Static<typeof dropsQuerySchema>
export const dropsQueryValidator = getValidator(dropsQuerySchema, queryValidator)
export const dropsQueryResolver = resolve<DropsQuery, HookContext>({})
