// TypeBox schema for errs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const errsSchema = Type.Object({
  _id: ObjectIdSchema(),
  type: Type.Optional(Type.String()),

  // Add only_in_json properties:
  method: Type.Optional(Type.String()),
  id: Type.Optional(Type.String()),
  error: Type.Optional(Type.Record(Type.String(), Type.Any())),
  params: Type.Optional(Type.Record(Type.String(), Type.Any())),
  result: Type.Optional(Type.Any()),

  // Fix type mismatch: data should be unknown (not object)
  data: Type.Optional(Type.Unknown()),

  // Keep existing fields that aren't marked as only_in_typebox:
  path: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Errs = Static<typeof errsSchema>
export const errsValidator = getValidator(errsSchema, dataValidator)
export const errsResolver = resolve<Errs, HookContext>({})
export const errsExternalResolver = resolve<Errs, HookContext>({})

export const errsDataSchema = Type.Object({
  ...Type.Omit(errsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ErrsData = Static<typeof errsDataSchema>
export const errsDataValidator = getValidator(errsDataSchema, dataValidator)
export const errsDataResolver = resolve<ErrsData, HookContext>({})

export const errsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(errsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ErrsPatch = Static<typeof errsPatchSchema>
export const errsPatchValidator = getValidator(errsPatchSchema, dataValidator)
export const errsPatchResolver = resolve<ErrsPatch, HookContext>({})

// Allow querying on any field from the main schema
const errsQueryProperties = errsSchema
export const errsQuerySchema = querySyntax(errsQueryProperties)
export type ErrsQuery = Static<typeof errsQuerySchema>
export const errsQueryValidator = getValidator(errsQuerySchema, queryValidator)
export const errsQueryResolver = resolve<ErrsQuery, HookContext>({})
