// TypeBox schema for ledgers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const ledgersSchema = Type.Object({
  _id: ObjectIdSchema(),
  account: ObjectIdSchema(),
  type: Type.Optional(Type.String()),

  // Keep existing fields that aren't marked as only_in_typebox:
  plan: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),

  // Fix type mismatch: planYear should be number (not string)
  planYear: Type.Optional(Type.Number()),

  // Add only_in_json properties:
  transactions: Type.Optional(Type.Array(ObjectIdSchema())),

  // Remove only_in_typebox properties (commenting out):
  // amount: Type.Number(), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // reference: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // referenceType: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // date: Type.Optional(Type.Any()), // only_in_typebox
  // balance: Type.Optional(Type.Number()), // only_in_typebox
  // runningBalance: Type.Optional(Type.Number()), // only_in_typebox
  // reconciled: Type.Optional(Type.Boolean()), // only_in_typebox
  // reconciledAt: Type.Optional(Type.Any()), // only_in_typebox
  // tags: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // metadata: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Ledgers = Static<typeof ledgersSchema>
export const ledgersValidator = getValidator(ledgersSchema, dataValidator)
export const ledgersResolver = resolve<Ledgers, HookContext>({})
export const ledgersExternalResolver = resolve<Ledgers, HookContext>({})

export const ledgersDataSchema = Type.Object({
  ...Type.Omit(ledgersSchema, ['_id']).properties
}, { additionalProperties: false })

export type LedgersData = Static<typeof ledgersDataSchema>
export const ledgersDataValidator = getValidator(ledgersDataSchema, dataValidator)
export const ledgersDataResolver = resolve<LedgersData, HookContext>({})

export const ledgersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(ledgersSchema, ['_id'])).properties
}, { additionalProperties: false })
export type LedgersPatch = Static<typeof ledgersPatchSchema>
export const ledgersPatchValidator = getValidator(ledgersPatchSchema, dataValidator)
export const ledgersPatchResolver = resolve<LedgersPatch, HookContext>({})

// Allow querying on any field from the main schema
const ledgersQueryProperties = ledgersSchema
export const ledgersQuerySchema = querySyntax(ledgersQueryProperties)
export type LedgersQuery = Static<typeof ledgersQuerySchema>
export const ledgersQueryValidator = getValidator(ledgersQuerySchema, queryValidator)
export const ledgersQueryResolver = resolve<LedgersQuery, HookContext>({})
