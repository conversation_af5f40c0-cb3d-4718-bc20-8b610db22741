// TypeBox schema for offers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const offersSchema = Type.Object({
  _id: ObjectIdSchema(),
  plan: ObjectIdSchema(),

  // Add only_in_json properties:
  fee: Type.Optional(Type.Number()),
  feeType: Type.Optional(Type.String()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(Type.String()),

  // Keep existing fields that aren't marked as only_in_typebox:
  contract: Type.Optional(Type.String()),
  role: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // coverage: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // price: Type.Optional(Type.Number()), // only_in_typebox
  // originalPrice: Type.Optional(Type.Number()), // only_in_typebox
  // discount: Type.Optional(Type.Number()), // only_in_typebox
  // discountType: Type.Optional(Type.String()), // only_in_typebox
  // validFrom: Type.Optional(Type.Any()), // only_in_typebox
  // validTo: Type.Optional(Type.Any()), // only_in_typebox
  // conditions: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // eligibility: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // maxUses: Type.Optional(Type.Number()), // only_in_typebox
  // currentUses: Type.Optional(Type.Number()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Offers = Static<typeof offersSchema>
export const offersValidator = getValidator(offersSchema, dataValidator)
export const offersResolver = resolve<Offers, HookContext>({})
export const offersExternalResolver = resolve<Offers, HookContext>({})

export const offersDataSchema = Type.Object({
  ...Type.Omit(offersSchema, ['_id']).properties
}, { additionalProperties: false })

export type OffersData = Static<typeof offersDataSchema>
export const offersDataValidator = getValidator(offersDataSchema, dataValidator)
export const offersDataResolver = resolve<OffersData, HookContext>({})

export const offersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(offersSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  fee: Type.Optional(Type.Number()),
  feeType: Type.Optional(Type.String()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(Type.String())
}, { additionalProperties: false })
export type OffersPatch = Static<typeof offersPatchSchema>
export const offersPatchValidator = getValidator(offersPatchSchema, dataValidator)
export const offersPatchResolver = resolve<OffersPatch, HookContext>({})

// Allow querying on any field from the main schema
const offersQueryProperties = offersSchema
export const offersQuerySchema = querySyntax(offersQueryProperties)
export type OffersQuery = Static<typeof offersQuerySchema>
export const offersQueryValidator = getValidator(offersQuerySchema, queryValidator)
export const offersQueryResolver = resolve<OffersQuery, HookContext>({})
