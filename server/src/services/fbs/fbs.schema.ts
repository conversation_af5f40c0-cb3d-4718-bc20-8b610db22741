// TypeBox schema for fbs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const fbsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),

  // Add only_in_json properties:
  owner: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  live: Type.Optional(Type.Boolean()),
  parent: Type.Optional(ObjectIdSchema()),
  children: Type.Optional(Type.Array(ObjectIdSchema())),
  primaryColor: Type.Optional(Type.String()),
  secondaryColor: Type.Optional(Type.String()),
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),
  welcomeTitle: Type.Optional(Type.String()),
  welcomeMessage: Type.Optional(Type.String()),
  welcomeImage: Type.Optional(Type.Record(Type.String(), Type.Any())),
  welcomeVideos: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  welcomeFiles: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  finishTitle: Type.Optional(Type.String()),
  finishMessage: Type.Optional(Type.String()),
  finishImage: Type.Optional(Type.Record(Type.String(), Type.Any())),
  finishVideos: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  finishFiles: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  dark: Type.Optional(Type.Boolean()),
  class: Type.Optional(Type.String()),
  products: Type.Optional(Type.Array(ObjectIdSchema())),
  style: Type.Optional(Type.Record(Type.String(), Type.Any())),
  fields: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  responses: Type.Optional(Type.Array(ObjectIdSchema())),
  canEdit: Type.Optional(Type.Array(ObjectIdSchema())),

  // Remove only_in_typebox properties (commenting out):
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox (but keep for now since not in all schemas)

  ...commonFields.properties
}, { additionalProperties: false })

export type Fbs = Static<typeof fbsSchema>
export const fbsValidator = getValidator(fbsSchema, dataValidator)
export const fbsResolver = resolve<Fbs, HookContext>({})
export const fbsExternalResolver = resolve<Fbs, HookContext>({})

export const fbsDataSchema = Type.Object({
  ...Type.Omit(fbsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FbsData = Static<typeof fbsDataSchema>
export const fbsDataValidator = getValidator(fbsDataSchema, dataValidator)
export const fbsDataResolver = resolve<FbsData, HookContext>({})

export const fbsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fbsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type FbsPatch = Static<typeof fbsPatchSchema>
export const fbsPatchValidator = getValidator(fbsPatchSchema, dataValidator)
export const fbsPatchResolver = resolve<FbsPatch, HookContext>({})

// Allow querying on any field from the main schema
const fbsQueryProperties = Type.Object({
  ...fbsSchema.properties,
  // Override name to allow any type for queries (type mismatch)
  name: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const fbsQuerySchema = querySyntax(fbsQueryProperties)
export type FbsQuery = Static<typeof fbsQuerySchema>
export const fbsQueryValidator = getValidator(fbsQuerySchema, queryValidator)
export const fbsQueryResolver = resolve<FbsQuery, HookContext>({})
