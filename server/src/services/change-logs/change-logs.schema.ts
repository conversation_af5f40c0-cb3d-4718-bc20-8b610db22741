// TypeBox schema for change-logs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const changeLogsSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Required fields from JSON Schema
  service: Type.String(),
  recordId: ObjectIdSchema(),

  // Remove TypeBox-only properties that aren't in JSON Schema:
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: true }) // Note: JSON Schema has additionalProperties: true

export type ChangeLogs = Static<typeof changeLogsSchema>
export const changeLogsValidator = getValidator(changeLogsSchema, dataValidator)
export const changeLogsResolver = resolve<ChangeLogs, HookContext>({})
export const changeLogsExternalResolver = resolve<ChangeLogs, HookContext>({})

export const changeLogsDataSchema = Type.Object({
  ...Type.Omit(changeLogsSchema, ['_id']).properties
}, { additionalProperties: true }) // Note: JSON Schema has additionalProperties: true

export type ChangeLogsData = Static<typeof changeLogsDataSchema>
export const changeLogsDataValidator = getValidator(changeLogsDataSchema, dataValidator)
export const changeLogsDataResolver = resolve<ChangeLogsData, HookContext>({})

export const changeLogsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(changeLogsSchema, ['_id'])).properties
}, { additionalProperties: true }) // Note: JSON Schema has additionalProperties: true
export type ChangeLogsPatch = Static<typeof changeLogsPatchSchema>
export const changeLogsPatchValidator = getValidator(changeLogsPatchSchema, dataValidator)
export const changeLogsPatchResolver = resolve<ChangeLogsPatch, HookContext>({})

// Allow querying on any field from the main schema
const changeLogsQueryProperties = Type.Object({
  ...changeLogsSchema.properties
}, { additionalProperties: true }) // Note: JSON Schema has additionalProperties: true

export const changeLogsQuerySchema = querySyntax(changeLogsQueryProperties)
export type ChangeLogsQuery = Static<typeof changeLogsQuerySchema>
export const changeLogsQueryValidator = getValidator(changeLogsQuerySchema, queryValidator)
export const changeLogsQueryResolver = resolve<ChangeLogsQuery, HookContext>({})
