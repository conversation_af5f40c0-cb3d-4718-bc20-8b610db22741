// TypeBox schema for enrollments service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, MandateSchema, addToSet } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const enrollmentsSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  group: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  status: Type.Optional(Type.String()),

  // Add only_in_json properties:
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  version: Type.Optional(Type.String()),
  planYear: Type.Optional(Type.Number()),
  address: Type.Optional(Type.Record(Type.String(), Type.Any())),
  county: Type.Optional(Type.String()),
  spec: Type.Optional(Type.Record(Type.String(), Type.Any())),
  statusNote: Type.Optional(Type.String()),
  optOut: Type.Optional(Type.Boolean()),
  open: Type.Optional(Type.Any()),
  close: Type.Optional(Type.Any()),
  terminated: Type.Optional(Type.Boolean()),
  terminatedAt: Type.Optional(Type.Any()),
  terminatedBy: Type.Optional(ObjectIdSchema()),
  enrolledAt: Type.Optional(Type.Any()),
  ichra: Type.Optional(Type.Record(Type.String(), Type.Any())),
  shop: Type.Optional(Type.Boolean()),
  type: Type.Optional(Type.String()),
  householdIncome: Type.Optional(Type.Number()),
  enrolled: Type.Optional(Type.Boolean()),
  cafe: Type.Optional(Type.Record(Type.String(), Type.Any())),
  lastClaimCoverage: Type.Optional(ObjectIdSchema()),
  claimPayments: Type.Optional(Type.Array(ObjectIdSchema())),
  patientClaims: Type.Optional(Type.Array(ObjectIdSchema())),
  coverageClaims: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  contributions: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),

  // Keep existing fields that aren't marked as only_in_typebox:
  idempotency_key: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // coverage: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // household: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // effectiveDate: Type.Optional(Type.Any()), // only_in_typebox
  // terminationDate: Type.Optional(Type.Any()), // only_in_typebox
  // premium: Type.Optional(Type.Number()), // only_in_typebox
  // subsidyAmount: Type.Optional(Type.Number()), // only_in_typebox
  // employerContribution: Type.Optional(Type.Number()), // only_in_typebox
  // deductibleMet: Type.Optional(Type.Number()), // only_in_typebox
  // oopMet: Type.Optional(Type.Number()), // only_in_typebox
  // mandate: Type.Optional(MandateSchema), // only_in_typebox
  // notes: Type.Optional(Type.String()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Enrollments = Static<typeof enrollmentsSchema>
export const enrollmentsValidator = getValidator(enrollmentsSchema, dataValidator)
export const enrollmentsResolver = resolve<Enrollments, HookContext>({})
export const enrollmentsExternalResolver = resolve<Enrollments, HookContext>({})

// Schema for creating new data
export const enrollmentsDataSchema = Type.Object({
  ...Type.Omit(enrollmentsSchema, ['_id']).properties
}, { additionalProperties: false })

export type EnrollmentsData = Static<typeof enrollmentsDataSchema>
export const enrollmentsDataValidator = getValidator(enrollmentsDataSchema, dataValidator)
export const enrollmentsDataResolver = resolve<EnrollmentsData, HookContext>({})

// Schema for updating existing data
export const enrollmentsPatchSchema = Type.Object({
  person: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(ObjectIdSchema()),
  household: Type.Optional(ObjectIdSchema()),
  group: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  status: Type.Optional(Type.String()),
  premium: Type.Optional(Type.Number()),
  subsidyAmount: Type.Optional(Type.Number()),
  employerContribution: Type.Optional(Type.Number()),
  deductibleMet: Type.Optional(Type.Number()),
  oopMet: Type.Optional(Type.Number()),
  mandate: Type.Optional(MandateSchema),
  notes: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number()))
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })

export type EnrollmentsPatch = Static<typeof enrollmentsPatchSchema>
export const enrollmentsPatchValidator = getValidator(enrollmentsPatchSchema, dataValidator)
export const enrollmentsPatchResolver = resolve<EnrollmentsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const enrollmentsQueryProperties = enrollmentsSchema

export const enrollmentsQuerySchema = querySyntax(enrollmentsQueryProperties)

export type EnrollmentsQuery = Static<typeof enrollmentsQuerySchema>
export const enrollmentsQueryValidator = getValidator(enrollmentsQuerySchema, queryValidator)
export const enrollmentsQueryResolver = resolve<EnrollmentsQuery, HookContext>({})
