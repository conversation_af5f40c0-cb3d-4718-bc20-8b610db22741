// TypeBox schema for contracts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, MandateSchema } from '../../utils/common/typebox-schemas.js'

export const contractsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),

  // Fix type mismatch: parties should be object (not array)
  parties: Type.Optional(Type.Record(Type.String(), Type.Any())),

  // Add only_in_json properties:
  public: Type.Optional(Type.Boolean()),
  template: Type.Optional(ObjectIdSchema()),
  subject: Type.Optional(ObjectIdSchema()),
  subjectService: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  owner: Type.Optional(ObjectIdSchema()),
  ownerService: Type.Optional(Type.String()),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  tags: Type.Optional(Type.Array(Type.String())),
  rejectedBy: Type.Optional(Type.Array(ObjectIdSchema())),
  pTags: Type.Optional(Type.Array(Type.String())),
  meta: Type.Optional(Type.Record(Type.String(), Type.Any())),
  sections: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),

  // Remove only_in_typebox properties (commenting out):
  // org: ObjectIdSchema(), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // plans: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // coverages: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // networks: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // providers: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox

  // Keep existing fields that aren't marked as only_in_typebox:
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  renewalDate: Type.Optional(Type.Any()),
  autoRenew: Type.Optional(Type.Boolean()),
  terms: Type.Optional(Type.String()),
  conditions: Type.Optional(Type.Array(Type.String())),
  documents: Type.Optional(Type.Array(ImageSchema)),
  signatures: Type.Optional(Type.Array(MandateSchema)),
  status: Type.Optional(Type.String()),
  value: Type.Optional(Type.Number()),
  currency: Type.Optional(Type.String()),
  paymentTerms: Type.Optional(Type.String()),
  penalties: Type.Optional(Type.Record(Type.String(), Type.Any())),
  amendments: Type.Optional(Type.Array(Type.Object({
    date: Type.Optional(Type.Any()),
    description: Type.Optional(Type.String()),
    document: Type.Optional(ImageSchema)
  }, { additionalProperties: false }))),
  // active: Type.Optional(Type.Boolean()), // This might be only_in_typebox, need to check

  ...commonFields.properties
}, { additionalProperties: false })

export type Contracts = Static<typeof contractsSchema>
export const contractsValidator = getValidator(contractsSchema, dataValidator)
export const contractsResolver = resolve<Contracts, HookContext>({})
export const contractsExternalResolver = resolve<Contracts, HookContext>({})

export const contractsDataSchema = Type.Object({
  ...Type.Omit(contractsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ContractsData = Static<typeof contractsDataSchema>
export const contractsDataValidator = getValidator(contractsDataSchema, dataValidator)
export const contractsDataResolver = resolve<ContractsData, HookContext>({})

export const contractsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(contractsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ContractsPatch = Static<typeof contractsPatchSchema>
export const contractsPatchValidator = getValidator(contractsPatchSchema, dataValidator)
export const contractsPatchResolver = resolve<ContractsPatch, HookContext>({})

// Allow querying on any field from the main schema
const contractsQueryProperties = Type.Object({
  ...contractsSchema.properties,
  // Add only_in_json query properties:
  _limit_to: Type.Optional(Type.Any()),
  // Override name to allow any type for queries (type mismatch)
  name: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const contractsQuerySchema = querySyntax(contractsQueryProperties)
export type ContractsQuery = Static<typeof contractsQuerySchema>
export const contractsQueryValidator = getValidator(contractsQuerySchema, queryValidator)
export const contractsQueryResolver = resolve<ContractsQuery, HookContext>({})
