// TypeBox schema for prices service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const pricesSchema = Type.Object({
  _id: ObjectIdSchema(),
  amount: Type.Number(),

  // Add only_in_json properties:
  notes: Type.Optional(Type.String()),
  session: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  eraser: Type.Optional(Type.Boolean()),
  providerName: Type.Optional(Type.String()),
  price: Type.Optional(Type.Number()),
  uom: Type.Optional(Type.String()),
  batch: Type.Optional(Type.String()),
  uid: Type.Optional(Type.String()),
  subject: Type.Optional(Type.String()),
  code: Type.Optional(Type.String()),
  billing_code: Type.Optional(Type.String()),
  carrier: Type.Optional(Type.String()),
  ndcs: Type.Optional(Type.Array(Type.String())),
  relatedCheckedAt: Type.Optional(Type.Any()),
  ndc: Type.Optional(Type.String()),
  ndc10: Type.Optional(Type.String()),
  ndc11: Type.Optional(Type.String()),
  labeler: Type.Optional(Type.String()),
  product: Type.Optional(Type.String()),
  package: Type.Optional(Type.String()),

  // Keep existing fields that aren't marked as only_in_typebox:
  provider: Type.Optional(ObjectIdSchema()),
  bundle: Type.Optional(Type.String()),
  source: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // currency: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // effectiveDate: Type.Optional(Type.Any()), // only_in_typebox
  // expirationDate: Type.Optional(Type.Any()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Prices = Static<typeof pricesSchema>
export const pricesValidator = getValidator(pricesSchema, dataValidator)
export const pricesResolver = resolve<Prices, HookContext>({})
export const pricesExternalResolver = resolve<Prices, HookContext>({})

export const pricesDataSchema = Type.Object({
  ...Type.Omit(pricesSchema, ['_id']).properties
}, { additionalProperties: false })

export type PricesData = Static<typeof pricesDataSchema>
export const pricesDataValidator = getValidator(pricesDataSchema, dataValidator)
export const pricesDataResolver = resolve<PricesData, HookContext>({})

export const pricesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(pricesSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  notes: Type.Optional(Type.String()),
  session: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  eraser: Type.Optional(Type.Boolean()),
  providerName: Type.Optional(Type.String()),
  price: Type.Optional(Type.Number()),
  uom: Type.Optional(Type.String()),
  batch: Type.Optional(Type.String()),
  uid: Type.Optional(Type.String()),
  subject: Type.Optional(Type.String()),
  code: Type.Optional(Type.String()),
  billing_code: Type.Optional(Type.String()),
  carrier: Type.Optional(Type.String()),
  ndcs: Type.Optional(Type.Array(Type.String())),
  relatedCheckedAt: Type.Optional(Type.Any()),
  ndc: Type.Optional(Type.String()),
  ndc10: Type.Optional(Type.String()),
  ndc11: Type.Optional(Type.String()),
  labeler: Type.Optional(Type.String()),
  product: Type.Optional(Type.String()),
  package: Type.Optional(Type.String()),

  // MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([]))
}, { additionalProperties: false })
export type PricesPatch = Static<typeof pricesPatchSchema>
export const pricesPatchValidator = getValidator(pricesPatchSchema, dataValidator)
export const pricesPatchResolver = resolve<PricesPatch, HookContext>({})

// Allow querying on any field from the main schema
const pricesQueryProperties = Type.Object({
  ...pricesSchema.properties,
  // Add only_in_json query properties:
  medicare: Type.Optional(Type.Number()),
  cash: Type.Optional(Type.Number()),
  medicare_low: Type.Optional(Type.Number()),
  medicare_high: Type.Optional(Type.Number()),
  cash_low: Type.Optional(Type.Number()),
  cash_high: Type.Optional(Type.Number()),
  estimatedAt: Type.Optional(Type.Any()),
  rxcui: Type.Optional(Type.String()),
  locationCode: Type.Optional(Type.String()),
  listPrice: Type.Optional(Type.Number()),
  files: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  zip_code: Type.Optional(Type.String()),
  // Fix type mismatch:
  alts: Type.Optional(Type.Array(Type.String())) // type_mismatch_json_array_typebox_string
}, { additionalProperties: false })

export const pricesQuerySchema = querySyntax(pricesQueryProperties)
export type PricesQuery = Static<typeof pricesQuerySchema>
export const pricesQueryValidator = getValidator(pricesQuerySchema, queryValidator)
export const pricesQueryResolver = resolve<PricesQuery, HookContext>({})
