// TypeBox schema for wallets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const walletsSchema = Type.Object({
  _id: ObjectIdSchema(),
  owner: ObjectIdSchema(),

  // Add only_in_json properties:
  ownerService: Type.Optional(Type.String()),
  methods: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),

  // Remove only_in_typebox properties (commenting out):
  // name: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // currency: Type.Optional(Type.String()), // only_in_typebox
  // balance: Type.Optional(Type.Number()), // only_in_typebox
  // available: Type.Optional(Type.Number()), // only_in_typebox
  // pending: Type.Optional(Type.Number()), // only_in_typebox
  // reserved: Type.Optional(Type.Number()), // only_in_typebox
  // creditLimit: Type.Optional(Type.Number()), // only_in_typebox
  // interestRate: Type.Optional(Type.Number()), // only_in_typebox
  // fees: Type.Optional(Type.Record(Type.String(), Type.Number())), // only_in_typebox
  // transactions: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // linkedAccounts: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // paymentMethods: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // autoReload: Type.Optional(Type.Object({...})), // only_in_typebox
  // limits: Type.Optional(Type.Object({...})), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // frozen: Type.Optional(Type.Boolean()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Wallets = Static<typeof walletsSchema>
export const walletsValidator = getValidator(walletsSchema, dataValidator)
export const walletsResolver = resolve<Wallets, HookContext>({})
export const walletsExternalResolver = resolve<Wallets, HookContext>({})

export const walletsDataSchema = Type.Object({
  ...Type.Omit(walletsSchema, ['_id']).properties
}, { additionalProperties: false })

export type WalletsData = Static<typeof walletsDataSchema>
export const walletsDataValidator = getValidator(walletsDataSchema, dataValidator)
export const walletsDataResolver = resolve<WalletsData, HookContext>({})

export const walletsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(walletsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  ownerService: Type.Optional(Type.String()),
  methods: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any())))
}, { additionalProperties: false })
export type WalletsPatch = Static<typeof walletsPatchSchema>
export const walletsPatchValidator = getValidator(walletsPatchSchema, dataValidator)
export const walletsPatchResolver = resolve<WalletsPatch, HookContext>({})

// Allow querying on any field from the main schema
const walletsQueryProperties = Type.Object({
  ...walletsSchema.properties,
  // Add only_in_json query properties (same as main schema):
  ownerService: Type.Optional(Type.String()),
  methods: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any())))
}, { additionalProperties: false })

export const walletsQuerySchema = querySyntax(walletsQueryProperties)
export type WalletsQuery = Static<typeof walletsQuerySchema>
export const walletsQueryValidator = getValidator(walletsQuerySchema, queryValidator)
export const walletsQueryResolver = resolve<WalletsQuery, HookContext>({})
