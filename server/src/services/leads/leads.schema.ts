// TypeBox schema for leads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema, ServiceAddressSchema } from '../../utils/common/typebox-schemas.js'

export const leadsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Remove all only_in_typebox properties (commenting out):
  // firstName: Type.Optional(Type.String()), // only_in_typebox
  // lastName: Type.Optional(Type.String()), // only_in_typebox
  // email: Type.Optional(Type.String()), // only_in_typebox
  // phone: Type.Optional(PhoneSchema), // only_in_typebox
  // address: Type.Optional(ServiceAddressSchema), // only_in_typebox
  // dateOfBirth: Type.Optional(Type.Any()), // only_in_typebox
  // gender: Type.Optional(Type.String()), // only_in_typebox
  // household: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // income: Type.Optional(Type.Number()), // only_in_typebox
  // householdSize: Type.Optional(Type.Number()), // only_in_typebox
  // source: Type.Optional(Type.String()), // only_in_typebox
  // campaign: Type.Optional(Type.String()), // only_in_typebox
  // referrer: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // stage: Type.Optional(Type.String()), // only_in_typebox
  // priority: Type.Optional(Type.String()), // only_in_typebox
  // assignedTo: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // notes: Type.Optional(Type.String()), // only_in_typebox
  // tags: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // interests: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // preferences: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // lastContact: Type.Optional(Type.Any()), // only_in_typebox
  // nextFollowUp: Type.Optional(Type.Any()), // only_in_typebox
  // converted: Type.Optional(Type.Boolean()), // only_in_typebox
  // convertedAt: Type.Optional(Type.Any()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Leads = Static<typeof leadsSchema>
export const leadsValidator = getValidator(leadsSchema, dataValidator)
export const leadsResolver = resolve<Leads, HookContext>({})
export const leadsExternalResolver = resolve<Leads, HookContext>({})

export const leadsDataSchema = Type.Object({
  ...Type.Omit(leadsSchema, ['_id']).properties
}, { additionalProperties: false })

export type LeadsData = Static<typeof leadsDataSchema>
export const leadsDataValidator = getValidator(leadsDataSchema, dataValidator)
export const leadsDataResolver = resolve<LeadsData, HookContext>({})

export const leadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(leadsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type LeadsPatch = Static<typeof leadsPatchSchema>
export const leadsPatchValidator = getValidator(leadsPatchSchema, dataValidator)
export const leadsPatchResolver = resolve<LeadsPatch, HookContext>({})

// Allow querying on any field from the main schema
const leadsQueryProperties = leadsSchema
export const leadsQuerySchema = querySyntax(leadsQueryProperties)
export type LeadsQuery = Static<typeof leadsQuerySchema>
export const leadsQueryValidator = getValidator(leadsQuerySchema, queryValidator)
export const leadsQueryResolver = resolve<LeadsQuery, HookContext>({})
