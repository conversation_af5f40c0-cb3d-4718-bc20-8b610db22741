// TypeBox schema for markets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema } from '../../utils/common/typebox-schemas.js'

export const marketsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),

  // Add only_in_json properties:
  hosts: Type.Optional(Type.Array(ObjectIdSchema())),
  owners: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  geo: Type.Optional(Type.Record(Type.String(), Type.Any())),
  locations: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),

  // Keep existing fields that aren't marked as only_in_typebox:
  zipCodes: Type.Optional(Type.Array(Type.String())),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  openEnrollmentStart: Type.Optional(Type.Any()),
  openEnrollmentEnd: Type.Optional(Type.Any()),
  specialEnrollmentPeriods: Type.Optional(Type.Array(Type.Object({
    name: Type.Optional(Type.String()),
    start: Type.Optional(Type.Any()),
    end: Type.Optional(Type.Any()),
    reason: Type.Optional(Type.String())
  }, { additionalProperties: false }))),

  // Remove only_in_typebox properties (commenting out):
  // state: Type.Optional(Type.String()), // only_in_typebox
  // region: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // plans: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // coverages: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // providers: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // networks: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // serviceArea: Type.Optional(Type.Array(ServiceAddressSchema)), // only_in_typebox
  // counties: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Markets = Static<typeof marketsSchema>
export const marketsValidator = getValidator(marketsSchema, dataValidator)
export const marketsResolver = resolve<Markets, HookContext>({})
export const marketsExternalResolver = resolve<Markets, HookContext>({})

export const marketsDataSchema = Type.Object({
  ...Type.Omit(marketsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MarketsData = Static<typeof marketsDataSchema>
export const marketsDataValidator = getValidator(marketsDataSchema, dataValidator)
export const marketsDataResolver = resolve<MarketsData, HookContext>({})

export const marketsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(marketsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type MarketsPatch = Static<typeof marketsPatchSchema>
export const marketsPatchValidator = getValidator(marketsPatchSchema, dataValidator)
export const marketsPatchResolver = resolve<MarketsPatch, HookContext>({})

// Allow querying on any field from the main schema
const marketsQueryProperties = marketsSchema
export const marketsQuerySchema = querySyntax(marketsQueryProperties)
export type MarketsQuery = Static<typeof marketsQuerySchema>
export const marketsQueryValidator = getValidator(marketsQuerySchema, queryValidator)
export const marketsQueryResolver = resolve<MarketsQuery, HookContext>({})
