// TypeBox schema for calendars service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, RRuleSchema } from '../../utils/common/typebox-schemas.js'

// Day schedule schema (matching JSON Schema version)
const DayScheduleSchema = Type.Object({
  all: Type.Optional(Type.Boolean()),
  times: Type.Optional(Type.Array(Type.Object({
    start: Type.Optional(Type.Number()), // min: 1, max: 2400
    end: Type.Optional(Type.Number())     // min: 1, max: 2400
  }, { additionalProperties: false })))
}, { additionalProperties: false })

// Schedule schema (matching JSON Schema version)
const ScheduleSchema = Type.Object({
  days: Type.Optional(Type.Object({
    '0': Type.Optional(DayScheduleSchema),
    '1': Type.Optional(DayScheduleSchema),
    '2': Type.Optional(DayScheduleSchema),
    '3': Type.Optional(DayScheduleSchema),
    '4': Type.Optional(DayScheduleSchema),
    '5': Type.Optional(DayScheduleSchema),
    '6': Type.Optional(DayScheduleSchema)
  }, { additionalProperties: false })),
  blackoutDates: Type.Optional(Type.Array(Type.Object({
    start: Type.Optional(Type.String()),
    end: Type.Optional(Type.String()),
    recurrence: Type.Optional(RRuleSchema)
  }, { additionalProperties: false })))
}, { additionalProperties: false })

// Notify schema (matching JSON Schema version)
const NotifySchema = Type.Object({
  active: Type.Optional(Type.Boolean()),
  contactPath: Type.Optional(Type.String()),
  contact: Type.Optional(Type.String())
}, { additionalProperties: false })

// Notifications schema (matching JSON Schema version)
const NotificationsSchema = Type.Object({
  before: Type.Optional(Type.Number()),
  sms: Type.Optional(NotifySchema),
  email: Type.Optional(NotifySchema),
  internal: Type.Optional(NotifySchema)
}, { additionalProperties: false })

// Tokens schema (matching JSON Schema version)
const TokensSchema = Type.Object({
  google: Type.Optional(Type.String())
}, { additionalProperties: false })

export const calendarsSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Properties from JSON Schema
  name: Type.Optional(Type.String()),
  ownerDefault: Type.Optional(Type.Boolean()),
  owner: Type.Optional(ObjectIdSchema()),
  ownerService: Type.Optional(Type.String()),
  editors: Type.Optional(Type.Array(ObjectIdSchema())),
  archived: Type.Optional(Type.Array(ObjectIdSchema())),
  past: Type.Optional(Type.Array(ObjectIdSchema())),
  future: Type.Optional(Type.Array(ObjectIdSchema())),
  notify: Type.Optional(NotificationsSchema),
  schedule: Type.Optional(ScheduleSchema),
  tokens: Type.Optional(TokensSchema),

  // Remove TypeBox-only properties that aren't in JSON Schema:
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Calendars = Static<typeof calendarsSchema>
export const calendarsValidator = getValidator(calendarsSchema, dataValidator)
export const calendarsResolver = resolve<Calendars, HookContext>({})
export const calendarsExternalResolver = resolve<Calendars, HookContext>({})

export const calendarsDataSchema = Type.Object({
  ...Type.Omit(calendarsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CalendarsData = Static<typeof calendarsDataSchema>
export const calendarsDataValidator = getValidator(calendarsDataSchema, dataValidator)
export const calendarsDataResolver = resolve<CalendarsData, HookContext>({})

export const calendarsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(calendarsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type CalendarsPatch = Static<typeof calendarsPatchSchema>
export const calendarsPatchValidator = getValidator(calendarsPatchSchema, dataValidator)
export const calendarsPatchResolver = resolve<CalendarsPatch, HookContext>({})

// Allow querying on any field from the main schema
const calendarsQueryProperties = calendarsSchema
export const calendarsQuerySchema = querySyntax(calendarsQueryProperties)
export type CalendarsQuery = Static<typeof calendarsQuerySchema>
export const calendarsQueryValidator = getValidator(calendarsQuerySchema, queryValidator)
export const calendarsQueryResolver = resolve<CalendarsQuery, HookContext>({})
