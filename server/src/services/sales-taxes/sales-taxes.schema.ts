// TypeBox schema for sales-taxes service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const salesTaxesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Add only_in_json properties:
  city: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  postal_code: Type.Optional(Type.String()),
  total_tax: Type.Optional(Type.Number()),
  taxes: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  expires: Type.Optional(Type.Any()),

  // Keep existing fields that aren't marked as only_in_typebox:
  country: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // name: Type.Optional(Type.String()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // status: Type.Optional(Type.String()), // only_in_typebox
  // data: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type SalesTaxes = Static<typeof salesTaxesSchema>
export const salesTaxesValidator = getValidator(salesTaxesSchema, dataValidator)
export const salesTaxesResolver = resolve<SalesTaxes, HookContext>({})
export const salesTaxesExternalResolver = resolve<SalesTaxes, HookContext>({})

export const salesTaxesDataSchema = Type.Object({
  ...Type.Omit(salesTaxesSchema, ['_id']).properties
}, { additionalProperties: false })

export type SalesTaxesData = Static<typeof salesTaxesDataSchema>
export const salesTaxesDataValidator = getValidator(salesTaxesDataSchema, dataValidator)
export const salesTaxesDataResolver = resolve<SalesTaxesData, HookContext>({})

export const salesTaxesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(salesTaxesSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  city: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  postal_code: Type.Optional(Type.String()),
  total_tax: Type.Optional(Type.Number()),
  taxes: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  expires: Type.Optional(Type.Any())
}, { additionalProperties: false })
export type SalesTaxesPatch = Static<typeof salesTaxesPatchSchema>
export const salesTaxesPatchValidator = getValidator(salesTaxesPatchSchema, dataValidator)
export const salesTaxesPatchResolver = resolve<SalesTaxesPatch, HookContext>({})

// Allow querying on any field from the main schema
const salesTaxesQueryProperties = Type.Object({
  ...salesTaxesSchema.properties,
  // Add only_in_json query properties (same as main schema):
  city: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  postal_code: Type.Optional(Type.String()),
  total_tax: Type.Optional(Type.Number()),
  taxes: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  expires: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const salesTaxesQuerySchema = querySyntax(salesTaxesQueryProperties)
export type SalesTaxesQuery = Static<typeof salesTaxesQuerySchema>
export const salesTaxesQueryValidator = getValidator(salesTaxesQuerySchema, queryValidator)
export const salesTaxesQueryResolver = resolve<SalesTaxesQuery, HookContext>({})
