// TypeBox schema for uploads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

export const uploadsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Add only_in_json properties:
  name: Type.Optional(Type.String()),
  cid: Type.Optional(Type.String()),
  originalname: Type.Optional(Type.String()),
  expires: Type.Optional(Type.Any()),
  usageVerified: Type.Optional(Type.Boolean()),
  usage: Type.Optional(Type.Record(Type.String(), Type.Any())),
  info: Type.Optional(Type.Record(Type.String(), Type.Any())),
  session: Type.Optional(Type.String()),
  temp: Type.Optional(Type.Boolean()),
  video: Type.Optional(Type.Record(Type.String(), Type.Any())),
  fileId: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  uploadChannel: Type.Optional(Type.String()),

  // Keep existing fields that aren't marked as only_in_typebox:
  url: Type.Optional(Type.String()),
  storage: Type.Optional(Type.String()),
  bucket: Type.Optional(Type.String()),
  tags: Type.Optional(Type.Array(Type.String())),

  // Remove only_in_typebox properties (commenting out):
  // filename: Type.String(), // only_in_typebox
  // originalName: Type.Optional(Type.String()), // only_in_typebox (but originalname is only_in_json)
  // mimetype: Type.Optional(Type.String()), // only_in_typebox
  // size: Type.Optional(Type.Number()), // only_in_typebox
  // path: Type.Optional(Type.String()), // only_in_typebox
  // key: Type.Optional(Type.String()), // only_in_typebox
  // uploadedBy: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // uploadedAt: Type.Optional(Type.Any()), // only_in_typebox
  // metadata: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // processed: Type.Optional(Type.Boolean()), // only_in_typebox
  // virus_scan: Type.Optional(Type.Object({...})), // only_in_typebox
  // access: Type.Optional(Type.String()), // only_in_typebox
  // permissions: Type.Optional(Type.Record(Type.String(), Type.Any())), // only_in_typebox
  // expiresAt: Type.Optional(Type.Any()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Uploads = Static<typeof uploadsSchema>
export const uploadsValidator = getValidator(uploadsSchema, dataValidator)
export const uploadsResolver = resolve<Uploads, HookContext>({})
export const uploadsExternalResolver = resolve<Uploads, HookContext>({})

export const uploadsDataSchema = Type.Object({
  ...Type.Omit(uploadsSchema, ['_id']).properties
}, { additionalProperties: false })

export type UploadsData = Static<typeof uploadsDataSchema>
export const uploadsDataValidator = getValidator(uploadsDataSchema, dataValidator)
export const uploadsDataResolver = resolve<UploadsData, HookContext>({})

export const uploadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(uploadsSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  name: Type.Optional(Type.String()),
  cid: Type.Optional(Type.String()),
  originalname: Type.Optional(Type.String()),
  expires: Type.Optional(Type.Any()),
  usageVerified: Type.Optional(Type.Boolean()),
  usage: Type.Optional(Type.Record(Type.String(), Type.Any())),
  info: Type.Optional(Type.Record(Type.String(), Type.Any())),
  session: Type.Optional(Type.String()),
  temp: Type.Optional(Type.Boolean()),
  video: Type.Optional(Type.Record(Type.String(), Type.Any())),
  fileId: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  uploadChannel: Type.Optional(Type.String()),

  // MongoDB operators
  $addToSet: Type.Optional(addToSet([]))
}, { additionalProperties: false })
export type UploadsPatch = Static<typeof uploadsPatchSchema>
export const uploadsPatchValidator = getValidator(uploadsPatchSchema, dataValidator)
export const uploadsPatchResolver = resolve<UploadsPatch, HookContext>({})

// Allow querying on any field from the main schema
const uploadsQueryProperties = Type.Object({
  ...uploadsSchema.properties,
  // Add only_in_json query properties:
  _limit_to: Type.Optional(Type.Any()),
  name: Type.Optional(Type.String()),
  cid: Type.Optional(Type.String()),
  originalname: Type.Optional(Type.String()),
  expires: Type.Optional(Type.Any()),
  usageVerified: Type.Optional(Type.Boolean()),
  usage: Type.Optional(Type.Record(Type.String(), Type.Any())),
  info: Type.Optional(Type.Record(Type.String(), Type.Any())),
  session: Type.Optional(Type.String()),
  temp: Type.Optional(Type.Boolean()),
  video: Type.Optional(Type.Record(Type.String(), Type.Any())),
  fileId: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  uploadChannel: Type.Optional(Type.String())
}, { additionalProperties: false })

export const uploadsQuerySchema = querySyntax(uploadsQueryProperties)
export type UploadsQuery = Static<typeof uploadsQuerySchema>
export const uploadsQueryValidator = getValidator(uploadsQuerySchema, queryValidator)
export const uploadsQueryResolver = resolve<UploadsQuery, HookContext>({})
