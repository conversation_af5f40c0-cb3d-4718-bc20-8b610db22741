// TypeBox schema for fb-res service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const fbResSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Keep existing fields that aren't marked as only_in_typebox:
  person: Type.Optional(ObjectIdSchema()),
  form: Type.Optional(Type.String()),

  // Fix type mismatch: formData should be unknown (not string)
  formData: Type.Optional(Type.Unknown()),

  // Add only_in_json properties:
  lastField: Type.Optional(Type.String()),


  ...commonFields.properties
}, { additionalProperties: false })

export type FbRes = Static<typeof fbResSchema>
export const fbResValidator = getValidator(fbResSchema, dataValidator)
export const fbResResolver = resolve<FbRes, HookContext>({})
export const fbResExternalResolver = resolve<FbRes, HookContext>({})

export const fbResDataSchema = Type.Object({
  ...Type.Omit(fbResSchema, ['_id']).properties
}, { additionalProperties: false })

export type FbResData = Static<typeof fbResDataSchema>
export const fbResDataValidator = getValidator(fbResDataSchema, dataValidator)
export const fbResDataResolver = resolve<FbResData, HookContext>({})

export const fbResPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fbResSchema, ['_id'])).properties
}, { additionalProperties: false })
export type FbResPatch = Static<typeof fbResPatchSchema>
export const fbResPatchValidator = getValidator(fbResPatchSchema, dataValidator)
export const fbResPatchResolver = resolve<FbResPatch, HookContext>({})

// Allow querying on any field from the main schema
const fbResQueryProperties = fbResSchema
export const fbResQuerySchema = querySyntax(fbResQueryProperties)
export type FbResQuery = Static<typeof fbResQuerySchema>
export const fbResQueryValidator = getValidator(fbResQuerySchema, queryValidator)
export const fbResQueryResolver = resolve<FbResQuery, HookContext>({})
