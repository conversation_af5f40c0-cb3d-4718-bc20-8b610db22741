// TypeBox schema for networks service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const networksSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),

  // Fix type mismatch: avatar should be object (not string)
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())),

  // Add only_in_json properties:
  subs: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  writers: Type.Optional(Type.Array(ObjectIdSchema())),
  images: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  lastSync: Type.Optional(Type.Record(Type.String(), Type.Any())),
  bundle_changes: Type.Optional(Type.Array(ObjectIdSchema())),
  bundles: Type.Optional(Type.Array(ObjectIdSchema())),
  bundle_reqs: Type.Optional(Type.Array(ObjectIdSchema())),
  plan_reqs: Type.Optional(Type.Array(ObjectIdSchema())),
  bundle_invites: Type.Optional(Type.Array(ObjectIdSchema())),
  plans_invites: Type.Optional(Type.Array(ObjectIdSchema())),

  // Keep existing fields that aren't marked as only_in_typebox:
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  access: Type.Optional(Type.String()),

  // Remove only_in_typebox properties (commenting out):
  // org: ObjectIdSchema(), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // providers: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // facilities: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // practitioners: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // coverages: Type.Optional(Type.Array(ObjectIdSchema())), // only_in_typebox
  // serviceArea: Type.Optional(Type.Array(ServiceAddressSchema)), // only_in_typebox
  // tier: Type.Optional(Type.String()), // only_in_typebox
  // contractNumber: Type.Optional(Type.String()), // only_in_typebox
  // effectiveDate: Type.Optional(Type.Any()), // only_in_typebox
  // terminationDate: Type.Optional(Type.Any()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Networks = Static<typeof networksSchema>
export const networksValidator = getValidator(networksSchema, dataValidator)
export const networksResolver = resolve<Networks, HookContext>({})
export const networksExternalResolver = resolve<Networks, HookContext>({})

export const networksDataSchema = Type.Object({
  ...Type.Omit(networksSchema, ['_id']).properties
}, { additionalProperties: false })

export type NetworksData = Static<typeof networksDataSchema>
export const networksDataValidator = getValidator(networksDataSchema, dataValidator)
export const networksDataResolver = resolve<NetworksData, HookContext>({})

export const networksPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(networksSchema, ['_id'])).properties,

  // Add only_in_json properties for patch (same as main schema):
  avatar: Type.Optional(Type.Record(Type.String(), Type.Any())), // Fix type mismatch
  subs: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  writers: Type.Optional(Type.Array(ObjectIdSchema())),
  images: Type.Optional(Type.Array(Type.Record(Type.String(), Type.Any()))),
  lastSync: Type.Optional(Type.Record(Type.String(), Type.Any())),
  bundle_changes: Type.Optional(Type.Array(ObjectIdSchema())),
  bundles: Type.Optional(Type.Array(ObjectIdSchema())),
  bundle_reqs: Type.Optional(Type.Array(ObjectIdSchema())),
  plan_reqs: Type.Optional(Type.Array(ObjectIdSchema())),
  bundle_invites: Type.Optional(Type.Array(ObjectIdSchema())),
  plans_invites: Type.Optional(Type.Array(ObjectIdSchema())),

  // MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([]))
}, { additionalProperties: false })
export type NetworksPatch = Static<typeof networksPatchSchema>
export const networksPatchValidator = getValidator(networksPatchSchema, dataValidator)
export const networksPatchResolver = resolve<NetworksPatch, HookContext>({})

// Allow querying on any field from the main schema
const networksQueryProperties = networksSchema
export const networksQuerySchema = querySyntax(networksQueryProperties)
export type NetworksQuery = Static<typeof networksQuerySchema>
export const networksQueryValidator = getValidator(networksQuerySchema, queryValidator)
export const networksQueryResolver = resolve<NetworksQuery, HookContext>({})
