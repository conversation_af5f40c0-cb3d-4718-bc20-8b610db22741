// TypeBox schema for cares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, pull, addToSet } from '../../utils/common/typebox-schemas.js'

// Exes schema (matching JSON Schema version)
const ExesSchema = Type.Object({
  id: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  medical_name: Type.Optional(Type.String()),
  code: Type.Optional(Type.String()),
  standard: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  loggedAt: Type.Optional(Type.Any()),
  loggedBy: Type.Optional(ObjectIdSchema())
}, { additionalProperties: false })

// Paids schema properties (from claims utils)
const PaidsSchema = Type.Object({
  pending: Type.Optional(Type.Number()),
  request: Type.Optional(Type.Number()),
  offer: Type.Optional(Type.Number()),
  paid: Type.Optional(Type.Number())
}, { additionalProperties: false })

export const caresSchema = Type.Object({
  _id: ObjectIdSchema(),
  // Required fields from JSON Schema
  person: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  patient: ObjectIdSchema(),

  // Properties from JSON Schema
  org: Type.Optional(ObjectIdSchema()),
  status: Type.Optional(Type.Union([
    Type.Literal(0), Type.Literal(1), Type.Literal(2),
    Type.Literal(3), Type.Literal(4), Type.Literal(5)
  ])), // Note: Number in JSON Schema, not String
  planPriority: Type.Optional(Type.Union([
    Type.Literal(0), Type.Literal(1), Type.Literal(2),
    Type.Literal(3), Type.Literal(4), Type.Literal(5)
  ])),
  patientPriority: Type.Optional(Type.Union([
    Type.Literal(0), Type.Literal(1), Type.Literal(2),
    Type.Literal(3), Type.Literal(4), Type.Literal(5)
  ])),
  providerPriority: Type.Optional(Type.Union([
    Type.Literal(0), Type.Literal(1), Type.Literal(2),
    Type.Literal(3), Type.Literal(4), Type.Literal(5)
  ])),
  initDate: Type.Optional(Type.Any()),
  name: Type.Optional(Type.String()),
  targetDate: Type.Optional(Type.Any()),
  lastVisit: Type.Optional(Type.Any()),
  visits: Type.Optional(Type.Array(ObjectIdSchema())),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  parent: Type.Optional(ObjectIdSchema()),
  children: Type.Optional(Type.Array(ObjectIdSchema())),
  related: Type.Optional(Type.Array(ObjectIdSchema())),
  conditions: Type.Optional(Type.Array(ExesSchema)),
  preventive: Type.Optional(Type.Boolean()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  files: Type.Optional(Type.Record(Type.String(), ImageSchema)),
  total: Type.Optional(Type.Number()),
  subtotal: Type.Optional(Type.Number()),
  ...PaidsSchema.properties,
  balance: Type.Optional(Type.Number()),
  balanceSyncedAt: Type.Optional(Type.Any()),

  // Remove TypeBox-only properties that aren't in JSON Schema:
  // provider: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // practitioner: Type.Optional(ObjectIdSchema()), // only_in_typebox
  // description: Type.Optional(Type.String()), // only_in_typebox
  // type: Type.Optional(Type.String()), // only_in_typebox
  // category: Type.Optional(Type.String()), // only_in_typebox
  // priority: Type.Optional(Type.String()), // only_in_typebox
  // startDate: Type.Optional(Type.Any()), // only_in_typebox
  // endDate: Type.Optional(Type.Any()), // only_in_typebox
  // goals: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // interventions: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // outcomes: Type.Optional(Type.Array(Type.String())), // only_in_typebox
  // notes: Type.Optional(Type.String()), // only_in_typebox
  // active: Type.Optional(Type.Boolean()), // only_in_typebox

  ...commonFields.properties
}, { additionalProperties: false })

export type Cares = Static<typeof caresSchema>
export const caresValidator = getValidator(caresSchema, dataValidator)
export const caresResolver = resolve<Cares, HookContext>({})
export const caresExternalResolver = resolve<Cares, HookContext>({})

export const caresDataSchema = Type.Object({
  ...Type.Omit(caresSchema, ['_id']).properties
}, { additionalProperties: false })

export type CaresData = Static<typeof caresDataSchema>
export const caresDataValidator = getValidator(caresDataSchema, dataValidator)
export const caresDataResolver = resolve<CaresData, HookContext>({})

export const caresPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(caresSchema, ['_id'])).properties,
  // MongoDB operators using utility functions (matching JSON Schema approach)
  $addToSet: Type.Optional(addToSet([
    { path: 'children', type: ObjectIdSchema() },
    { path: 'conditions', type: ExesSchema }
  ])),
  $pull: Type.Optional(pull([
    { path: 'children', type: ObjectIdSchema() },
    { path: 'conditions', type: ExesSchema }
  ]))
}, { additionalProperties: false })
export type CaresPatch = Static<typeof caresPatchSchema>
export const caresPatchValidator = getValidator(caresPatchSchema, dataValidator)
export const caresPatchResolver = resolve<CaresPatch, HookContext>({})

// Allow querying on any field from the main schema
const caresQueryProperties = Type.Object({
  ...caresSchema.properties,
  // Query-specific properties
  _limit_to: Type.Optional(Type.Any()),
  // Override name to allow any type for queries (matching JSON Schema)
  name: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const caresQuerySchema = querySyntax(caresQueryProperties)
export type CaresQuery = Static<typeof caresQuerySchema>
export const caresQueryValidator = getValidator(caresQuerySchema, queryValidator)
export const caresQueryResolver = resolve<CaresQuery, HookContext>({})
